<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="项目报价" prop="projectQuotationId">
        <el-select v-model="queryParams.projectQuotationId" placeholder="请选择项目报价" clearable>
          <el-option
            v-for="quotation in quotationList"
            :key="quotation.id"
            :label="quotation.projectName"
            :value="quotation.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="周期序号" prop="cycleNumber">
        <el-input
          v-model="queryParams.cycleNumber"
          placeholder="请输入周期序号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.detection_cycle_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleGenerate"
          v-hasPermi="['sampling:cycle-item:generate']"
        >生成条目</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Refresh"
          @click="handleRegenerate"
          v-hasPermi="['sampling:cycle-item:regenerate']"
        >重新生成</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="multiple"
          @click="handleBatchUpdateStatus"
          v-hasPermi="['sampling:cycle-item:edit']"
        >批量更新状态</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="handleDeleteByProject"
          v-hasPermi="['sampling:cycle-item:remove']"
        >删除项目条目</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['sampling:cycle-item:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cycleItemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="项目报价" align="center" prop="projectQuotationName" />
      <el-table-column label="周期序号" align="center" prop="cycleNumber" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="120">
        <template #default="scope">
          <dict-tag :options="dict.type.detection_cycle_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="分配任务" align="center" prop="samplingTaskNumber" width="150" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleDetail(scope.row)"
            v-hasPermi="['sampling:cycle-item:query']"
          >详情</el-button>
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdateStatus(scope.row)"
            v-hasPermi="['sampling:cycle-item:edit']"
          >更新状态</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 生成检测周期条目对话框 -->
    <el-dialog title="生成检测周期条目" v-model="generateOpen" width="500px" append-to-body>
      <el-form ref="generateForm" :model="generateForm" :rules="generateRules" label-width="100px">
        <el-form-item label="项目报价" prop="projectQuotationId">
          <el-select v-model="generateForm.projectQuotationId" placeholder="请选择项目报价">
            <el-option
              v-for="quotation in quotationList"
              :key="quotation.id"
              :label="quotation.projectName"
              :value="quotation.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
        <el-button type="primary" @click="submitGenerate">确 定</el-button>
        <el-button @click="generateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重新生成检测周期条目对话框 -->
    <el-dialog title="重新生成检测周期条目" v-model="regenerateOpen" width="500px" append-to-body>
      <el-form ref="regenerateForm" :model="regenerateForm" :rules="regenerateRules" label-width="100px">
        <el-form-item label="项目报价" prop="projectQuotationId">
          <el-select v-model="regenerateForm.projectQuotationId" placeholder="请选择项目报价">
            <el-option
              v-for="quotation in quotationList"
              :key="quotation.id"
              :label="quotation.projectName"
              :value="quotation.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRegenerate">确 定</el-button>
          <el-button @click="regenerateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量更新状态对话框 -->
    <el-dialog title="批量更新状态" v-model="batchUpdateOpen" width="400px" append-to-body>
      <el-form ref="batchUpdateForm" :model="batchUpdateForm" :rules="batchUpdateRules" label-width="80px">
        <el-form-item label="新状态" prop="status">
          <el-select v-model="batchUpdateForm.status" placeholder="请选择新状态">
            <el-option
              v-for="dict in dict.type.detection_cycle_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchUpdate">确 定</el-button>
          <el-button @click="batchUpdateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 更新状态对话框 -->
    <el-dialog title="更新状态" v-model="updateStatusOpen" width="400px" append-to-body>
      <el-form ref="updateStatusForm" :model="updateStatusForm" :rules="updateStatusRules" label-width="80px">
        <el-form-item label="新状态" prop="status">
          <el-select v-model="updateStatusForm.status" placeholder="请选择新状态">
            <el-option
              v-for="dict in dict.type.detection_cycle_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitUpdateStatus">确 定</el-button>
          <el-button @click="updateStatusOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除项目条目对话框 -->
    <el-dialog title="删除项目条目" :visible.sync="deleteProjectOpen" width="500px" append-to-body>
      <el-form ref="deleteProjectForm" :model="deleteProjectForm" :rules="deleteProjectRules" label-width="100px">
        <el-form-item label="项目报价" prop="projectQuotationId">
          <el-select v-model="deleteProjectForm.projectQuotationId" placeholder="请选择项目报价">
            <el-option
              v-for="quotation in quotationList"
              :key="quotation.id"
              :label="quotation.projectName"
              :value="quotation.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="submitDeleteProject">确 定</el-button>
        <el-button @click="deleteProjectOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 条目详情对话框 -->
    <el-dialog title="条目详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">{{ cycleItemDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="项目报价">{{ cycleItemDetail.projectQuotationName }}</el-descriptions-item>
        <el-descriptions-item label="周期序号">{{ cycleItemDetail.cycleNumber }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.detection_cycle_status" :value="cycleItemDetail.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="分配任务">{{ cycleItemDetail.samplingTaskNumber }}</el-descriptions-item>
        <el-descriptions-item label="创建用户">{{ cycleItemDetail.createUserName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(cycleItemDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(cycleItemDetail.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listDetectionCycleItem, 
  getDetectionCycleItem, 
  generateCycleItems, 
  regenerateCycleItems,
  updateCycleItemStatus,
  batchUpdateCycleItemStatus,
  deleteCycleItemsByProjectQuotationId
} from "@/api/sampling/detectionCycleItem";
import { listProjectQuotation } from "@/api/quotation/projectQuotation";

export default {
  name: "DetectionCycleItem",
  dicts: ['detection_cycle_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检测周期条目表格数据
      cycleItemList: [],
      // 是否显示生成弹出层
      generateOpen: false,
      // 是否显示重新生成弹出层
      regenerateOpen: false,
      // 是否显示批量更新弹出层
      batchUpdateOpen: false,
      // 是否显示更新状态弹出层
      updateStatusOpen: false,
      // 是否显示删除项目弹出层
      deleteProjectOpen: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectQuotationId: null,
        cycleNumber: null,
        status: null
      },
      // 生成表单参数
      generateForm: {
        projectQuotationId: null
      },
      // 重新生成表单参数
      regenerateForm: {
        projectQuotationId: null
      },
      // 批量更新表单参数
      batchUpdateForm: {
        status: null
      },
      // 更新状态表单参数
      updateStatusForm: {
        itemId: null,
        status: null
      },
      // 删除项目表单参数
      deleteProjectForm: {
        projectQuotationId: null
      },
      // 条目详情
      cycleItemDetail: {},
      // 表单校验
      generateRules: {
        projectQuotationId: [
          { required: true, message: "项目报价不能为空", trigger: "change" }
        ]
      },
      regenerateRules: {
        projectQuotationId: [
          { required: true, message: "项目报价不能为空", trigger: "change" }
        ]
      },
      batchUpdateRules: {
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      updateStatusRules: {
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      },
      deleteProjectRules: {
        projectQuotationId: [
          { required: true, message: "项目报价不能为空", trigger: "change" }
        ]
      },
      // 项目报价列表
      quotationList: []
    };
  },
  created() {
    this.getList();
    this.getQuotationList();
  },
  methods: {
    /** 查询检测周期条目列表 */
    getList() {
      this.loading = true;
      listDetectionCycleItem(this.queryParams).then(response => {
        this.cycleItemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询项目报价列表 */
    getQuotationList() {
      listProjectQuotation({ status: 'approved' }).then(response => {
        this.quotationList = response.rows;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 生成条目按钮操作 */
    handleGenerate() {
      this.generateForm = { projectQuotationId: null };
      this.generateOpen = true;
    },
    /** 重新生成按钮操作 */
    handleRegenerate() {
      this.regenerateForm = { projectQuotationId: null };
      this.regenerateOpen = true;
    },
    /** 批量更新状态按钮操作 */
    handleBatchUpdateStatus() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要更新的条目");
        return;
      }
      this.batchUpdateForm = { status: null };
      this.batchUpdateOpen = true;
    },
    /** 更新状态按钮操作 */
    handleUpdateStatus(row) {
      this.updateStatusForm = {
        itemId: row.id,
        status: row.status
      };
      this.updateStatusOpen = true;
    },
    /** 删除项目条目按钮操作 */
    handleDeleteByProject() {
      this.deleteProjectForm = { projectQuotationId: null };
      this.deleteProjectOpen = true;
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const itemId = row.id;
      getDetectionCycleItem(itemId).then(response => {
        this.cycleItemDetail = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交生成 */
    submitGenerate() {
      this.$refs["generateForm"].validate(valid => {
        if (valid) {
          generateCycleItems(this.generateForm.projectQuotationId).then(response => {
            this.$modal.msgSuccess("生成成功");
            this.generateOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 提交重新生成 */
    submitRegenerate() {
      this.$refs["regenerateForm"].validate(valid => {
        if (valid) {
          this.$modal.confirm('重新生成将删除现有条目，是否继续？').then(() => {
            regenerateCycleItems(this.regenerateForm.projectQuotationId).then(response => {
              this.$modal.msgSuccess("重新生成成功");
              this.regenerateOpen = false;
              this.getList();
            });
          });
        }
      });
    },
    /** 提交批量更新 */
    submitBatchUpdate() {
      this.$refs["batchUpdateForm"].validate(valid => {
        if (valid) {
          batchUpdateCycleItemStatus(this.ids, this.batchUpdateForm.status).then(response => {
            this.$modal.msgSuccess("批量更新成功");
            this.batchUpdateOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 提交更新状态 */
    submitUpdateStatus() {
      this.$refs["updateStatusForm"].validate(valid => {
        if (valid) {
          updateCycleItemStatus(this.updateStatusForm.itemId, this.updateStatusForm.status).then(response => {
            this.$modal.msgSuccess("更新成功");
            this.updateStatusOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 提交删除项目条目 */
    submitDeleteProject() {
      this.$refs["deleteProjectForm"].validate(valid => {
        if (valid) {
          this.$modal.confirm('是否确认删除该项目的所有检测周期条目？').then(() => {
            deleteCycleItemsByProjectQuotationId(this.deleteProjectForm.projectQuotationId).then(response => {
              this.$modal.msgSuccess("删除成功");
              this.deleteProjectOpen = false;
              this.getList();
            });
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('sampling/cycle-item/export', {
        ...this.queryParams
      }, `cycle_item_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>