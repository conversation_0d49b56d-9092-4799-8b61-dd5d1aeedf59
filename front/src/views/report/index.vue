<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="周报" name="weekly">
        <report-list 
          ref="weeklyReportRef"
          :report-type="'weekly'"
          :key="'weekly'"
        />
      </el-tab-pane>
      <el-tab-pane label="月报" name="monthly">
        <report-list 
          ref="monthlyReportRef"
          :report-type="'monthly'"
          :key="'monthly'"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="Report">
import ReportList from './components/ReportList.vue'

const activeTab = ref('weekly')

/** 切换Tab */
function handleTabClick(tab) {
  activeTab.value = tab.name
  // 刷新对应的列表
  if (tab.name === 'weekly') {
    nextTick(() => {
      proxy.$refs.weeklyReportRef?.getList()
    })
  } else if (tab.name === 'monthly') {
    nextTick(() => {
      proxy.$refs.monthlyReportRef?.getList()
    })
  }
}

const { proxy } = getCurrentInstance()
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
