<template>
  <div>
    <el-form ref="equipmentRef" :model="form" :rules="rules" label-width="180px">
      <!-- 一、基础信息类 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">一、基础信息类</span>
      </el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备编号" prop="equipmentNumber">
            <el-input v-model="form.equipmentNumber" placeholder="请输入设备编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="测量设备名称" prop="equipmentName">
            <el-input v-model="form.equipmentName" placeholder="请输入测量设备（器具）名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="启用日期" prop="enableDate">
            <el-date-picker
              v-model="form.enableDate"
              type="date"
              placeholder="请选择启用日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备类型" prop="equipmentType">
            <el-input v-model="form.equipmentType" placeholder="请输入设备类型" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="型号规格" prop="modelSpecification">
            <el-input v-model="form.modelSpecification" placeholder="请输入型号规格" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出厂编号" prop="factoryNumber">
            <el-input v-model="form.factoryNumber" placeholder="请输入出厂编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="制造商" prop="manufacturer">
            <el-input v-model="form.manufacturer" placeholder="请输入制造商" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 二、技术参数类 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">二、技术参数类</span>
      </el-divider>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="指标特性" prop="indicatorCharacteristics">
            <el-input
              v-model="form.indicatorCharacteristics"
              type="textarea"
              :rows="3"
              placeholder="请输入指标特性"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 三、量值溯源管理类 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">三、量值溯源管理类</span>
      </el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检定/校准/核查机构" prop="calibrationInstitution">
            <el-input v-model="form.calibrationInstitution" placeholder="请输入检定/校准/核查机构" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="溯源方式" prop="traceabilityMethod">
            <el-select v-model="form.traceabilityMethod" placeholder="请选择溯源方式" style="width: 100%">
              <el-option label="检定" value="检定" />
              <el-option label="校准" value="校准" />
              <el-option label="核查" value="核查" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="本次检定/校准/核查日期" prop="currentCalibrationDate">
            <el-date-picker
              v-model="form.currentCalibrationDate"
              type="date"
              placeholder="请选择本次检定/校准/核查日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="间隔日期(天)" prop="intervalDays">
            <el-input-number
              v-model="form.intervalDays"
              :min="1"
              :max="9999"
              placeholder="请输入间隔日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="下次检定/校准/核查日期" prop="nextCalibrationDate">
            <el-date-picker
              v-model="form.nextCalibrationDate"
              type="date"
              placeholder="请选择下次检定/校准/核查日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="期间核查日期" prop="interimCheckDate">
            <el-date-picker
              v-model="form.interimCheckDate"
              type="date"
              placeholder="请选择期间核查日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="证书编号" prop="certificateNumber">
            <el-input v-model="form.certificateNumber" placeholder="请输入证书编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检定/校准/核查内容" prop="calibrationContent">
            <el-input
              v-model="form.calibrationContent"
              type="textarea"
              :rows="4"
              placeholder="请输入检定/校准/核查内容"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注(溯源结果确认)" prop="calibrationRemark">
            <el-input
              v-model="form.calibrationRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注(溯源结果确认)"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 附件上传 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件" prop="attachments">
            <div class="attachment-section">
              <!-- 文件上传 -->
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :file-list="fileList"
                multiple
                :limit="10"
                :show-file-list="true"
              >
                <el-button type="primary" :icon="Upload">选择文件</el-button>
                <el-button
                  type="success"
                  @click="uploadFiles"
                  :disabled="fileList.length === 0"
                  :loading="uploading"
                >
                  上传附件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 PDF、Word、Excel、图片、文本文件，单个文件不超过10MB，最多上传10个文件
                  </div>
                </template>
              </el-upload>

              <!-- 已上传文件列表 -->
              <div v-if="form.attachments && form.attachments.length > 0" class="uploaded-files">
                <h4>已上传文件：</h4>
                <div class="file-list">
                  <div
                    v-for="(file, index) in form.attachments"
                    :key="index"
                    class="file-item"
                  >
                    <span class="file-name">
                      <el-link underline @click="downAttachMent(index)">{{ file.fileName }}</el-link>
                    </span>
                    <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
                    <el-button
                      type="danger"
                      size="small"
                      :icon="Delete"
                      @click="removeAttachment(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 四、管理责任类 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">四、管理责任类</span>
      </el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管理者" prop="manager">
            <el-input v-model="form.manager" placeholder="请输入管理者" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="equipmentStatus">
            <el-select v-model="form.equipmentStatus" placeholder="请选择设备状态" style="width: 100%">
              <el-option label="正常" value="正常" />
              <el-option label="维修中" value="维修中" />
              <el-option label="停用" value="停用" />
              <el-option label="报废" value="报废" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.equipmentStatus === '停用'">
          <el-form-item
            label="设备状态说明"
            prop="equipmentStatusDescription"
            :rules="form.equipmentStatus === '停用' ? [{ required: true, message: '停用状态必须填写说明', trigger: 'blur' }] : []"
          >
            <el-input v-model="form.equipmentStatusDescription" placeholder="请输入停用原因说明" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="放置地点" prop="location">
            <el-input v-model="form.location" placeholder="请输入放置地点" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 五、财务与合同类 -->
      <el-divider content-position="left">
        <span style="font-weight: bold; color: #409EFF;">五、财务与合同类</span>
      </el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="金额" prop="amount">
            <el-input-number
              v-model="form.amount"
              :precision="2"
              :min="0"
              placeholder="请输入金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同" prop="contract">
            <el-input v-model="form.contract" placeholder="请输入合同" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发票" prop="invoice">
            <el-input v-model="form.invoice" placeholder="请输入发票" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <div slot="footer" class="dialog-footer" style="text-align: center; margin-top: 20px;">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script setup>
import { Upload, Delete } from '@element-plus/icons-vue'
import { getToken } from "@/utils/auth"
import { addEquipmentManagement, updateEquipmentManagement, delEquipmentAttachMent, downEquipmentAttachMent} 
from "@/api/basedata/equipmentManagement"

const { proxy } = getCurrentInstance()
const emit = defineEmits(['submit', 'cancel'])

// 上传相关配置
const fileList = ref([])
const uploadRef = ref()
const uploading = ref(false)

const props = defineProps({
  equipmentData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const form = ref({
  id: null,
  equipmentNumber: '',
  equipmentName: '',
  enableDate: null,
  equipmentType: '',
  modelSpecification: '',
  factoryNumber: '',
  manufacturer: '',
  indicatorCharacteristics: '',
  calibrationInstitution: '',
  traceabilityMethod: '',
  currentCalibrationDate: null,
  certificateNumber: '',
  nextCalibrationDate: null,
  intervalDays: null,
  interimCheckDate: null,
  calibrationRemark: '',
  calibrationContent: '',
  attachments: [],
  manager: '',
  equipmentStatus: '',
  statusDescription: '',
  location: '',
  amount: null,
  contract: '',
  invoice: ''
})

const rules = {
  equipmentNumber: [
    { required: true, message: "设备编号不能为空", trigger: "blur" }
  ],
  equipmentName: [
    { required: true, message: "设备名称不能为空", trigger: "blur" }
  ]
}

// 监听props变化
watch(() => props.equipmentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    form.value = { ...newVal }
  }
}, { immediate: true, deep: true })

/** 提交按钮 */
function submitForm() {
  proxy.$refs["equipmentRef"].validate(valid => {
    if (valid) {
      if (props.isEdit) {
        updateEquipmentManagement(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          emit('submit')
        })
      } else {
        addEquipmentManagement(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          emit('submit')
        })
      }
    }
  })
}

/** 取消按钮 */
function cancel() {
  emit('cancel')
}

// 附件处理方法
const beforeUpload = (file) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/jpg',
    'text/plain'
  ]

  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    proxy.$modal.msgError('只能上传 PDF、Word、Excel、图片、文本文件!')
    return false
  }
  if (!isLt10M) {
    proxy.$modal.msgError('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleFileChange = (file, files) => {
  // 文件选择变化时更新文件列表
  fileList.value = files
}

const handleRemove = (file, files) => {
  // 从文件列表中移除文件
  fileList.value = files
}

const uploadFiles = async () => {
  if (fileList.value.length === 0) {
    proxy.$modal.msgWarning('请先选择要上传的文件')
    return
  }

  uploading.value = true

  try {
    // 创建FormData
    const formData = new FormData()
    fileList.value.forEach(fileItem => {
      formData.append('files', fileItem.raw)
    })

    // 调用上传API
    const response = await fetch(
      import.meta.env.VITE_APP_BASE_API + '/equipment-management/attachment/upload',
      {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + getToken()
        },
        body: formData
      }
    )

    const result = await response.json()

    if (result.code === 200) {
      // 将上传成功的文件信息添加到表单数据中
      if (!form.value.attachments) {
        form.value.attachments = []
      }
      form.value.attachments.push(...result.data)

      // 清空文件列表
      fileList.value = []
      uploadRef.value.clearFiles()

      proxy.$modal.msgSuccess(`成功上传 ${result.data.length} 个文件`)
    } else {
      proxy.$modal.msgError(result.msg || '文件上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    proxy.$modal.msgError('文件上传失败: ' + error.message)
  } finally {
    uploading.value = false
  }
}

const removeAttachment = (index) => {
  // 调用删除接口
  proxy.$modal.confirm('确定要删除该附件吗？').then(() => {
    // 这里可以调用删除文件的API
    // 调用上传API
    let file_path = form.value.attachments[index].filePath;
    delEquipmentAttachMent(file_path).then(response => {
        form.value.attachments.splice(index, 1)
        proxy.$modal.msgSuccess("附件删除成功")
    })
  })
}

const downAttachMent = (index) => {
  // 调用删除接口
  let file_path = form.value.attachments[index].filePath;
  let file_name = form.value.attachments[index].fileName;
  const response = downEquipmentAttachMent(file_path)

  // 根据格式设置MIME类型和文件扩展名
  const mimeTypes = {
    excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    word: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    pdf: 'application/pdf',
    stream: 'octet-stream',
    jpeg: 'image/jpeg',
    png: 'image/png',
    jpg: 'image/jpg',
    txt: 'text/plain'
  }

  const extensions = file_name.split('.').pop().toLowerCase()

  // 创建下载链接
  let blob
  if (mimeTypes[extensions]) {
    blob = new Blob([response], {
      type: mimeTypes[extensions]
    })
  }else{
    blob = new Blob([response], {
      type: mimeTypes["stream"]
    })
  }
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = file_name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
  proxy.$modal.msgSuccess("附件下载成功")

}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}

.el-divider__text {
  font-size: 16px;
}

.attachment-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.uploaded-files {
  margin-top: 15px;
}

.uploaded-files h4 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.file-name {
  flex: 1;
  color: #606266;
  font-size: 14px;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #909399;
  font-size: 12px;
  margin-right: 10px;
  min-width: 60px;
}
</style>
