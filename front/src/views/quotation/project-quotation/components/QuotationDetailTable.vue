<template>
  <div>
    <div style="margin-bottom: 20px">
      <el-button type="primary" @click="openQuotationDetail"><el-icon><Document /></el-icon>配置检测明细项目</el-button>
      <project-quotation-item-import @import-success="handleImportSuccess" style="margin-left: 10px;" />
    </div>
    <div v-if="quotationDetailData.length > 0">
      <el-table :data="quotationDetailData" style="width: 100%" border @filter-change="handleFilterChange">
        <!-- 检测项目信息 -->
        <el-table-column label="检测项目" align="center" fixed>
          <el-table-column
            label="分类" 
            fixed
            prop="classification" 
            width="100" 
            sortable 
            show-overflow-tooltip
            :filters="getUniqueValues('classification')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="二级分类" 
            fixed
            prop="category"
            width="120" 
            sortable 
            show-overflow-tooltip
            :filters="getUniqueValues('category')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="指标" 
            fixed
            prop="parameter" 
            width="150" 
            sortable 
            show-overflow-tooltip 
            :filters="getUniqueValues('parameter')"
            :filter-method="filterByValue"
          />
          <el-table-column           
            label="方法" 
            fixed
            prop="method" 
            width="200" 
            sortable 
            show-overflow-tooltip 
            :filters="getUniqueValues('method')"
            :filter-method="filterByValue"
          />
        </el-table-column>

        <!-- 采样信息 -->
        <el-table-column label="采样信息" align="center">
          <el-table-column 
            label="样品来源" 
            prop="sampleSource" 
            width="100"
            sortable 
            show-overflow-tooltip  
            :filters="getUniqueValues('sampleSource')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="点位名称" 
            prop="pointName" 
            width="120"
            sortable 
            show-overflow-tooltip  
            :filters="getUniqueValues('pointName')"
            :filter-method="filterByValue"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.pointName" placement="top">
                <span>{{ row.pointName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column 
            label="点位数" 
            prop="pointCount" 
            width="80">
            <template #default="{ row }">
              <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                <span>{{ row.pointCount }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column 
            label="周期类型" 
            prop="cycleType" 
            width="80"
            :filters="getUniqueValues('cycleType')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="检测周期数" 
            prop="cycleCount" 
            width="100"
          />
          <el-table-column 
            label="检测频次数" 
            prop="frequency" 
            width="100">
            <template #default="{ row }">
              <el-tooltip content="检测频次(次/检测周期)" placement="top">
                <span>{{ row.frequency }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column 
            label="样品数" 
            prop="sampleCount" 
            width="80">
            <template #default="{ row }">
              <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                <span>{{ row.sampleCount }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="分包方式"
            prop="subcontractMethod"
            sortable
            :filters="getUniqueValues('subcontractMethod')"
            :filter-method="filterByValue"
            width="120">
            <template #default="{ row }">
              <el-tag
                :type="row.subcontractMethod === '分包' ? 'warning' :
                       row.subcontractMethod === '分包检测' ? 'info' : 'success'"
              >
                {{ row.subcontractMethod || '不分包' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 其他信息 -->
        <el-table-column label="其他信息" align="center">
          <el-table-column 
            label="备注" 
            prop="remark" 
            width="150" 
            show-overflow-tooltip
            :filters="getUniqueValues('remark')"
            :filter-method="filterByValue"
          />
        </el-table-column>
      </el-table>

      <div class="fee-summary">
        <span>检测明细项目数：{{ quotationDetailData.length }}</span>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-empty description="请配置检测明细项目" />
    </div>

    <!-- 检测明细项目配置组件 -->
    <QuotationDetail
      :visible="quotationDetailOpen"
      :initial-data="quotationDetailState"
      @update:visible="quotationDetailOpen = $event"
      @confirm="handleQuotationDetailConfirm"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import QuotationDetail from '@/components/QuotationDetail/index.vue'
import ProjectQuotationItemImport from './ProjectQuotationItemImport.vue'
import { defineProps, defineEmits } from 'vue'

const quotationDetailData = ref([])

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})
// 筛选条件
const filters = ref({})
// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    console.log('配置检测明细项目的 modelValue 发生变化:', {
      oldValue,
      newValue
    })
    quotationDetailData.value = newValue
    filters.value = {}
  },
  { deep: true }
)

const emit = defineEmits(['update:modelValue', 'filter-change'])



// 计算唯一值
const getUniqueValues = (prop) => {
  const uniqueValues = [...new Set(quotationDetailData.value.map(item => item[prop]))]
  return uniqueValues.filter(value => value !== undefined).map(value => ({ text: value, value }))
}

// 处理筛选变化
const handleFilterChange = (filters) => {
  emit('filter-change', filters)
}

// 是否显示检测明细项目配置
const quotationDetailOpen = ref(false)
// 检测明细项目组件的内部状态数据
const quotationDetailState = ref({
  categories: [],
  testItemsData: {},
  samplingPointsData: {}
})

// 打开检测明细项目配置
function openQuotationDetail() {
  quotationDetailOpen.value = true
  quotationDetailState.value = convertToCurrentState(quotationDetailData.value)
}

function convertToCurrentState(flattenedData) {
  const result = {
    categories: new Set(),
    testItemsData: {},
    samplingPointsData: {}
  }
  
  const processedTestItems = new Set()

  flattenedData.forEach(item => {
    const { category, classification, method, parameter, qualificationCode } = item
    
    // 添加分类
    result.categories.add(category)
    
    // 生成唯一标识 groupKey
    const groupKey = `${category}_${method}_${parameter}_${qualificationCode}`
    
    // 处理测试项目（去重）
    if (!processedTestItems.has(groupKey)) {
      processedTestItems.add(groupKey)
      
      if (!result.testItemsData[category]) {
        result.testItemsData[category] = []
      }
      
      result.testItemsData[category].push({
        id: item.id,
        groupKey,
        category,
        categoryCode: item.categoryCode,
        classification,
        parameter,
        method,
        qualificationCode,
        limitationScope: item.limitationScope,
        remark: item.remark
      })
    }
    
    // 处理采样点数据
    if (item.pointName) {
      if (!result.samplingPointsData[groupKey]) {
        result.samplingPointsData[groupKey] = []
      }
      
      result.samplingPointsData[groupKey].push({
        id: item.id,
        pointName: item.pointName,
        pointCount: item.pointCount,
        cycleType: item.cycleType,
        cycleCount: item.cycleCount,
        frequency: item.frequency,
        sampleCount: item.sampleCount,
        sampleSource: item.sampleSource,
        subcontractMethod: item.subcontractMethod
      })
    }
  })

  let flattenedRenderData = {
    categories: Array.from(result.categories),
    testItemsData: result.testItemsData,
    samplingPointsData: result.samplingPointsData
  }
  console.log('配置检测明细项目的 转换后的扁平化数据:', flattenedRenderData)
  return flattenedRenderData
}

// 处理检测明细项目配置确认
function handleQuotationDetailConfirm(data, componentState) {
  // 保存组件的内部状态，以便下次打开时恢复
  if (componentState) {
    quotationDetailState.value = {
      categories: componentState.categories || [],
      testItemsData: componentState.testItemsData || {},
      samplingPointsData: componentState.samplingPointsData || {}
    }
  }
  quotationDetailOpen.value = false
  ElMessage.success('检测明细项目配置成功')
  quotationDetailData.value = data
  console.log('检测明细项目配置成功:', data)
  emit('update:modelValue', data)
}

// 处理批量导入成功
function handleImportSuccess(importedData) {
  console.log('批量导入成功:', importedData)

  // 将导入的数据添加到现有数据中
  const newData = [...quotationDetailData.value, ...importedData]
  quotationDetailData.value = newData

  // 更新组件状态
  quotationDetailState.value = convertToCurrentState(newData)

  // 通知父组件
  emit('update:modelValue', newData)

  ElMessage.success(`成功导入 ${importedData.length} 条检测明细项目`)
}

// 在 <script setup> 中添加过滤方法
const filterByValue = (value, row, column) => {
  const property = column['property']
  return row[property] === value
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
}
.empty-state {
  margin-top: 20px;
  text-align: center;
}
</style>