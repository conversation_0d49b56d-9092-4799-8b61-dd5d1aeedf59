<template>
  <div class="contract-detail">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="基本信息" name="basic">
          <!-- 一、基本信息 -->
          <el-divider content-position="left">
            <span style="font-weight: bold; color: #409EFF;">一、基本信息</span>
          </el-divider>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="合同名称">{{ contractData.contractName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同编号">{{ contractData.contractNumber || '-' }}</el-descriptions-item>
            <el-descriptions-item label="外部合同编号">{{ contractData.externalContractNumber || '-' }}</el-descriptions-item>
            <el-descriptions-item label="业务类型">{{ contractData.businessType || '-' }}</el-descriptions-item>
            <el-descriptions-item label="区域">
              {{ (contractData.regionProvince && contractData.regionCity) ?
                 `${contractData.regionProvince} ${contractData.regionCity}` : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="委托单位">{{ contractData.clientName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="委托单位联系人">{{ contractData.clientContact || '-' }}</el-descriptions-item>
            <el-descriptions-item label="取得方式">{{ contractData.acquisitionMethod || '-' }}</el-descriptions-item>
            <el-descriptions-item label="项目负责人">
              <span v-if="contractData.projectManagerNames && contractData.projectManagerNames.length > 0">
                <el-tag
                  v-for="(manager, index) in contractData.projectManagerNames"
                  :key="index"
                  size="small"
                  style="margin: 2px"
                  type="primary"
                >
                  {{ manager }}
                </el-tag>
              </span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目客服">{{ contractData.projectServiceName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="所属部门">{{ contractData.deptName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同签订日期">{{ contractData.contractSignDate || '-' }}</el-descriptions-item>
          </el-descriptions>

          <!-- 二、金额信息 -->
          <el-divider content-position="left">
            <span style="font-weight: bold; color: #409EFF;">二、金额信息</span>
          </el-divider>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="金额类型">{{ contractData.amountType || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同金额">
              {{ contractData.contractAmount ? '¥' + Number(contractData.contractAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="报价单总金额">
              {{ contractData.quotationTotalAmount ? '¥' + Number(contractData.quotationTotalAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="变更后合同金额">
              {{ contractData.changedContractAmount ? '¥' + Number(contractData.changedContractAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="外协金额">
              {{ contractData.outsourcingAmount ? '¥' + Number(contractData.outsourcingAmount).toLocaleString() : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="外协单位">{{ contractData.outsourcingCompany || '-' }}</el-descriptions-item>
          </el-descriptions>

          <!-- 三、其他信息 -->
          <el-divider content-position="left">
            <span style="font-weight: bold; color: #409EFF;">三、其他信息</span>
          </el-divider>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="完工时间">{{ parseTime(contractData.completionTime) || '-' }}</el-descriptions-item>
            <el-descriptions-item label="合同状态">
              <el-tag
                v-if="contractData.contractStatus"
                :type="getStatusType(contractData.contractStatus)"
              >
                {{ contractData.contractStatus }}
              </el-tag>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建人">{{ contractData.createBy || '-' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ parseTime(contractData.createTime) || '-' }}</el-descriptions-item>
            <el-descriptions-item label="更新人">{{ contractData.updateBy || '-' }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ parseTime(contractData.updateTime) || '-' }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ contractData.remark || '-' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="商务信息" name="business">
          <ContractBusiness
            :contract-id="contractData.id"
            :readonly="true"
          />
        </el-tab-pane>
      </el-tabs>

      <!-- 关闭按钮 -->
      <div style="text-align: center; margin-top: 20px;">
        <el-button @click="cancel">关 闭</el-button>
      </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { parseTime } from '@/utils/ruoyi'
import ContractBusiness from './ContractBusiness.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  contractData: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: '合同详情'
  }
})

const emit = defineEmits(['update:visible', 'cancel'])

// 对话框显示状态
const open = ref(false)
const activeTab = ref('basic')

// 监听visible变化
watch(() => props.visible, (newVal) => {
  open.value = newVal
  console.log("visible:", newVal)
})

// 监听open变化
watch(open, (newVal) => {
  console.log("open:", newVal)
  emit('update:visible', newVal)
})

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '草稿':
      return 'info'
    case '待签订':
      return 'warning'
    case '执行中':
      return 'primary'
    case '已完成':
      return 'success'
    case '已终止':
      return 'danger'
    default:
      return 'info'
  }
}

// 取消
const cancel = () => {
  open.value = false
  emit('cancel')
}
</script>

<style scoped>
.contract-detail {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-tab-pane) {
  overflow-y: auto;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-tag {
  margin: 2px;
}
</style>