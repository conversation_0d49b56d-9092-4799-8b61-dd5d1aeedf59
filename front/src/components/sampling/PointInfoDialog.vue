<template>
  <el-dialog
    v-if="!inline"
    v-model="visible"
    title="点位信息查看"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-if="loading" class="loading-container" v-loading="loading">
      <div style="height: 200px; display: flex; align-items: center; justify-content: center;">
        <p>加载中...</p>
      </div>
    </div>
    
    <div v-else-if="pointInfo" class="point-info-container">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>点位名称：</label>
              <span>{{ pointInfo.pointName || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>采样时间：</label>
              <span>{{ formatDateTime(pointInfo.samplingTime) || '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 经纬度信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>位置信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>经度：</label>
              <span>{{ pointInfo.longitude || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>纬度：</label>
              <span>{{ pointInfo.latitude || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>海拔高度：</label>
              <span>{{ pointInfo.altitude ? `${pointInfo.altitude}米` : '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 环境信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>环境信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <label>天气状况：</label>
              <span>{{ pointInfo.weatherCondition || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>温度：</label>
              <span>{{ pointInfo.temperature ? `${pointInfo.temperature}°C` : '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>湿度：</label>
              <span>{{ pointInfo.humidity ? `${pointInfo.humidity}%` : '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>风速：</label>
              <span>{{ pointInfo.windSpeed ? `${pointInfo.windSpeed}m/s` : '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">
          <el-col :span="12">
            <div class="info-item">
              <label>风向：</label>
              <span>{{ pointInfo.windDirection || '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 点位照片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>点位照片</span>
          </div>
        </template>
        <div class="photo-container">
          <div v-if="pointInfo.pointPhotoUrl" class="photo-item">
            <el-image
              :src="getImageUrl(pointInfo.pointPhotoUrl)"
              :preview-src-list="[getImageUrl(pointInfo.pointPhotoUrl)]"
              fit="cover"
              class="photo-image"
            />
            <p class="photo-label">采样点位照片</p>
          </div>
          <div v-else class="no-photo">
            <el-icon><Picture /></el-icon>
            <p>暂无点位照片</p>
          </div>
        </div>
      </el-card>

      <!-- 环境照片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>环境照片</span>
          </div>
        </template>
        <div class="photo-grid">
          <div class="photo-item">
            <div v-if="pointInfo.eastPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.eastPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">东侧环境</p>
          </div>

          <div class="photo-item">
            <div v-if="pointInfo.southPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.southPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">南侧环境</p>
          </div>

          <div class="photo-item">
            <div v-if="pointInfo.westPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.westPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">西侧环境</p>
          </div>

          <div class="photo-item">
            <div v-if="pointInfo.northPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.northPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">北侧环境</p>
          </div>
        </div>
      </el-card>

      <!-- 备注信息 -->
      <el-card v-if="pointInfo.remarks" class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>备注信息</span>
          </div>
        </template>
        <div class="remarks-content">
          {{ pointInfo.remarks }}
        </div>
      </el-card>
    </div>
    
    <div v-else class="no-data">
      <el-empty description="暂无点位信息" />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 内联模式 -->
  <div v-else class="point-info-inline">
    <div v-if="loading" class="loading-container" v-loading="loading">
      <div style="height: 200px; display: flex; align-items: center; justify-content: center;">
        <p>加载中...</p>
      </div>
    </div>

    <div v-else-if="pointInfo" class="point-info-container">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>点位名称：</label>
              <span>{{ pointInfo.pointName || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>采样时间：</label>
              <span>{{ formatDateTime(pointInfo.samplingTime) || '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 经纬度信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>位置信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>经度：</label>
              <span>{{ pointInfo.longitude || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>纬度：</label>
              <span>{{ pointInfo.latitude || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>海拔高度：</label>
              <span>{{ pointInfo.altitude ? `${pointInfo.altitude}米` : '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 环境信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>环境信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <label>天气状况：</label>
              <span>{{ pointInfo.weatherCondition || '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>温度：</label>
              <span>{{ pointInfo.temperature ? `${pointInfo.temperature}°C` : '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>湿度：</label>
              <span>{{ pointInfo.humidity ? `${pointInfo.humidity}%` : '未设置' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>风速：</label>
              <span>{{ pointInfo.windSpeed ? `${pointInfo.windSpeed}m/s` : '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">
          <el-col :span="12">
            <div class="info-item">
              <label>风向：</label>
              <span>{{ pointInfo.windDirection || '未设置' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 点位照片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>点位照片</span>
          </div>
        </template>
        <div class="photo-container">
          <div v-if="pointInfo.pointPhotoUrl" class="photo-item">
            <el-image
              :src="getImageUrl(pointInfo.pointPhotoUrl)"
              :preview-src-list="[getImageUrl(pointInfo.pointPhotoUrl)]"
              fit="cover"
              class="photo-image"
            />
            <p class="photo-label">采样点位照片</p>
          </div>
          <div v-else class="no-photo">
            <el-icon><Picture /></el-icon>
            <p>暂无点位照片</p>
          </div>
        </div>
      </el-card>

      <!-- 环境照片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>环境照片</span>
          </div>
        </template>
        <div class="photo-grid">
          <div class="photo-item">
            <div v-if="pointInfo.eastPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.eastPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">东侧环境</p>
          </div>

          <div class="photo-item">
            <div v-if="pointInfo.southPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.southPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">南侧环境</p>
          </div>

          <div class="photo-item">
            <div v-if="pointInfo.westPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.westPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">西侧环境</p>
          </div>

          <div class="photo-item">
            <div v-if="pointInfo.northPhotoUrl" class="photo-wrapper">
              <el-image
                :src="getImageUrl(pointInfo.northPhotoUrl)"
                :preview-src-list="environmentPhotos"
                fit="cover"
                class="photo-image"
              />
            </div>
            <div v-else class="no-photo">
              <el-icon><Picture /></el-icon>
            </div>
            <p class="photo-label">北侧环境</p>
          </div>
        </div>
      </el-card>

      <!-- 备注信息 -->
      <el-card v-if="pointInfo.remarks" class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>备注信息</span>
          </div>
        </template>
        <div class="remarks-content">
          {{ pointInfo.remarks }}
        </div>
      </el-card>
    </div>

    <div v-else class="no-data">
      <el-empty description="暂无点位信息" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Picture } from '@element-plus/icons-vue'
import { getPointInfoByGroupId } from '@/api/sampling/pointInfo'
import { parseTime } from '@/utils/ruoyi'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupId: {
    type: Number,
    default: null
  },
  inline: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const pointInfo = ref(null)

// 计算属性
const environmentPhotos = computed(() => {
  const photos = []
  if (pointInfo.value?.eastPhotoUrl) photos.push(getImageUrl(pointInfo.value.eastPhotoUrl))
  if (pointInfo.value?.southPhotoUrl) photos.push(getImageUrl(pointInfo.value.southPhotoUrl))
  if (pointInfo.value?.westPhotoUrl) photos.push(getImageUrl(pointInfo.value.westPhotoUrl))
  if (pointInfo.value?.northPhotoUrl) photos.push(getImageUrl(pointInfo.value.northPhotoUrl))
  return photos
})

// 方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}')
}

const getImageUrl = (url) => {
  if (!url) return ''
  // 如果URL已经是完整的HTTP URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  // 如果是相对路径，添加基础URL
  return `${import.meta.env.VITE_APP_BASE_URL || 'http://127.0.0.1:9099'}${url}`
}

const loadPointInfo = async () => {
  if (!props.groupId) return
  
  loading.value = true
  try {
    const response = await getPointInfoByGroupId(props.groupId)
    if (response.code === 200) {
      pointInfo.value = response.data
    } else {
      pointInfo.value = null
    }
  } catch (error) {
    console.error('获取点位信息失败:', error)
    pointInfo.value = null
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  pointInfo.value = null
}

// 监听器
watch(() => props.groupId, (newVal) => {
  if (newVal && (visible.value || props.inline)) {
    loadPointInfo()
  }
})

watch(visible, (newVal) => {
  if (newVal && props.groupId) {
    loadPointInfo()
  }
})

// 内联模式下，组件挂载时自动加载数据
watch(() => props.inline, (newVal) => {
  if (newVal && props.groupId) {
    loadPointInfo()
  }
}, { immediate: true })
</script>

<style scoped>
.loading-container {
  text-align: center;
  padding: 50px;
}

.point-info-container {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.info-item {
  margin-bottom: 10px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.photo-container {
  text-align: center;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.photo-item {
  text-align: center;
}

.photo-wrapper {
  width: 200px;
  height: 150px;
  margin: 0 auto;
}

.photo-image {
  width: 200px;
  height: 150px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.photo-label {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

.no-photo {
  width: 200px;
  height: 150px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  color: #c0c4cc;
}

.no-photo .el-icon {
  font-size: 40px;
  margin-bottom: 8px;
}

.remarks-content {
  padding: 10px;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
}

.no-data {
  text-align: center;
  padding: 50px;
}

/* 内联模式样式 */
.point-info-inline {
  width: 100%;
}

.point-info-inline .point-info-container {
  max-height: none;
  overflow-y: visible;
}

.point-info-inline .info-card {
  margin-bottom: 15px;
}

.point-info-inline .photo-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.point-info-inline .photo-image {
  width: 150px;
  height: 120px;
}

.point-info-inline .photo-wrapper {
  width: 150px;
  height: 120px;
  margin: 0 auto;
}

.point-info-inline .no-photo {
  width: 150px;
  height: 120px;
  margin: 0 auto;
}
</style>
