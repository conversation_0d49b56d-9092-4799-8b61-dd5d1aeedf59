<template>
  <el-dropdown :trigger="['click']" @command="handleMenuClick">
    <el-button icon="Download">
      导出
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="excel">
           导出Excel
        </el-dropdown-item>
        <el-dropdown-item command="pdf">
          导出PDF
        </el-dropdown-item>
        <el-dropdown-item command="word">
           导出Word
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script>
import { defineComponent } from 'vue';
// import { ExportOutlined, FileExcelOutlined, FilePdfOutlined, FileWordOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'ExportButton',
  components: {
   
  },
  props: {
    // 导出接口URL
    exportUrl: {
      type: String,
      required: true
    },
    // 查询参数
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    handleMenuClick(exportType) {
      //const exportType = e.target.command;
      this.exportData(exportType);
    },
    exportData(exportType) {
      // 构建查询参数
      const params = new URLSearchParams();
      params.append('export_type', exportType);

      // 添加其他查询参数
      for (const key in this.queryParams) {
        if (this.queryParams[key] !== null && this.queryParams[key] !== undefined && this.queryParams[key] !== '') {
          params.append(key, this.queryParams[key]);
        }
      }

      // 构建完整URL
      const url = `${this.exportUrl}?${params.toString()}`;

      // 打开新窗口下载文件
      window.open(url, '_blank');
    }
  }
});
</script>

<style scoped>
.ant-btn {
  margin-right: 8px;
}
</style>
