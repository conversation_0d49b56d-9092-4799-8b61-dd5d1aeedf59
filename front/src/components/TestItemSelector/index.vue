<template>
  <div class="test-item-selector">
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="100%"
      append-to-body
      @close="handleClose"
    >
      <div class="selector-content">
        <!-- 顶部信息 -->
        <el-alert title="筛选条件：" type="success" :closable="false" />
        <div class="info-bar">
          <span class="category-info">检测类别：{{ category }}</span>
          <el-input
          v-model="searchParameter"
          placeholder="请输入检测参数,多个逗号分割"
          style="width: 200px;"
          clearable
          @input="handleParameterInput"
          @clear="handleClear"
          />
          <el-input
            v-model="searchMethod"
            placeholder="请输入检测方法,多个逗号分割"
            style="width: 30%;"
            clearable
            @input="handleMethodInput"
            @clear="handleClear"
          />
          <el-button type="primary" @click="handleSearch" :loading="searchLoading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>

        <!-- 主体内容 -->
        <div class="selector-main">
          <!-- 左侧：待选表格 -->
          <div class="left-panel">
            <div class="panel-header">
              <h4><el-icon><Expand /></el-icon>
                  可选列表           
                  <span class="count-info">
                  待选：{{ availableList.length }} 项 | 已选：{{ selectedList.length }} 项
                </span>
              </h4>
              <el-button
                type="primary"
                :disabled="availableSelection.length === 0"
                @click="addToSelected"
                icon="Right"
              >
                添加到已选 ({{ availableSelection.length }})
              </el-button>
            </div>

            <el-table
              ref="availableTableRef"
              v-loading="loading"
              :data="availableList"
              @selection-change="handleAvailableSelectionChange"
              max-height="200"
              style="width: 100%"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column 
                label="检测参数" 
                prop="parameter" 
                fixed  
                min-width="120" 
                :show-overflow-tooltip="true"
                :filters="parameterFilters"
                :filter-method="filterByColumn('parameter')"
              />
              <el-table-column 
                label="检测方法" 
                prop="method" 
                min-width="150" 
                :show-overflow-tooltip="true"
                :filters="methodFilters"
                :filter-method="filterByColumn('method')"
              />
              <el-table-column 
                label="唯一编号" 
                prop="qualificationCode" 
                min-width="120"
                :filters="qualificationCodeFilters"
                :filter-method="filterByColumn('qualificationCode')"
              />
              <el-table-column 
                label="限制范围" 
                prop="limitationScope" 
                min-width="150" 
                :show-overflow-tooltip="true"
                :filters="limitationScopeFilters"
                :filter-method="filterByColumn('limitationScope')"
              />
            </el-table>

            <!-- 分页 -->
            <el-pagination
              v-show="total > 0"
              :total="total"
              :page-size="pageSize"
              :current-page="currentPage"
              layout="total, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
              style="margin-top: 15px; text-align: center;"
            />
          </div>
        </div>
        <div class="selector-main">
          <!-- 右侧：已选表格 -->
          <div class="right-panel">
            <div class="panel-header">
              <h4> <el-icon><Expand /></el-icon>已选列表</h4>
              <el-button
                type="danger"
                :disabled="selectedSelection.length === 0"
                @click="removeFromSelected"
                icon="Left"
              >
                移除选中 ({{ selectedSelection.length }})
              </el-button>
            </div>

            <el-table
              ref="selectedTableRef"
              :data="selectedList"
              @selection-change="handleSelectedSelectionChange"
              min-height="20"
              style="width: 100%"
            >
              <el-table-column 
                type="selection" 
                width="55" 
                align="center"
                :filters="[]"
                :filter-method="() => true"
              />
              <el-table-column 
                label="检测参数" 
                prop="parameter" 
                fixed  
                min-width="120" 
                :show-overflow-tooltip="true"
                :filters="[{ text: '全部', value: '' }]"
                :filter-method="filterByColumn('parameter')"
              />
              <el-table-column 
                label="检测方法" 
                prop="method" 
                min-width="150" 
                :show-overflow-tooltip="true"
                :filters="[{ text: '全部', value: '' }]"
                :filter-method="filterByColumn('method')"
              />
              <el-table-column 
                label="资质编号" 
                prop="qualificationCode" 
                min-width="120"
                :filters="[{ text: '全部', value: '' }]"
                :filter-method="filterByColumn('qualificationCode')"
              />
              <el-table-column 
                label="限制范围" 
                prop="limitationScope" 
                min-width="150" 
                :show-overflow-tooltip="true"
                :filters="[{ text: '全部', value: '' }]"
                :filter-method="filterByColumn('limitationScope')"
              />
              <el-table-column label="备注" width="100">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.remark"
                    placeholder="请输入备注"
                    size="small"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, computed } from 'vue'
import { pageTechnicalManual } from '@/api/basedata/technicalManual'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

const searchMethod = ref('')
const searchParameter = ref('')
const searchLoading = ref(false)
let searchTimer = null

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择检测项目'
  },
  category: {
    type: String,
    default: ''
  },
  selectedItems: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 对话框可见性
const dialogVisible = ref(false)
// 加载状态
const loading = ref(false)
// 待选列表
const availableList = ref([])
// 已选列表
const selectedList = ref([])
// 待选表格选中项
const availableSelection = ref([])
// 已选表格选中项
const selectedSelection = ref([])
// 分页信息
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
// 表格引用
const availableTableRef = ref(null)
const selectedTableRef = ref(null)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initData()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听category变化
watch(() => props.category, () => {
  if (dialogVisible.value && props.category) {
    loadAvailableItems()
  }
})

// 监听selectedItems变化
watch(() => props.selectedItems, (newVal) => {
  selectedList.value = newVal.map(item => ({
    ...item,
    remark: item.remark || ''
  }))
}, { immediate: true, deep: true })

// 初始化数据
async function initData() {
  // 重置状态
  currentPage.value = 1
  availableSelection.value = []
  selectedSelection.value = []

  // 初始化已选列表
  selectedList.value = props.selectedItems.map(item => ({
    ...item,
    remark: item.remark || ''
  }))

  // 加载待选项目
  if (props.category) {
    await loadAvailableItems()
  }
}

// 加载待选项目
async function loadAvailableItems(filter) {
  if (!props.category) {
    ElMessage.warning('请先选择检测类别')
    return
  }

  if(!filter){
    // 默认过滤条件
    filter = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      category: props.category
    }
  }

  // 清理空值参数，提高查询效率
  const cleanFilter = Object.fromEntries(
    Object.entries(filter).filter(([, value]) =>
      value !== null && value !== undefined && value !== ''
    )
  )

  loading.value = true
  try {
    const response = await pageTechnicalManual(cleanFilter)
    const data = response.data
    total.value = data.total

    // 过滤掉已选中的项目
    const selectedIds = selectedList.value.map(item => item.id)
    availableList.value = data.rows.filter(item => !selectedIds.includes(item.id))

    // 根据查询条件显示不同的提示信息
    if (data.rows.length === 0) {
      const hasSearchCondition = cleanFilter.method || cleanFilter.parameter
      if (hasSearchCondition) {
        ElMessage.info('未找到符合条件的可选检测项目，请尝试调整搜索条件')
      } else {
        ElMessage.info('该类别下暂无可选检测项目')
      }
    }

  } catch (error) {
    console.error('加载检测项目失败:', error)
    ElMessage.error('加载检测项目失败')
  } finally {
    loading.value = false
  }
}

// 处理分页变化
function handleSizeChange(page) {
  pageSize.value = page
  handleSearch()
}

// 处理分页变化
function handlePageChange(page) {
  currentPage.value = page
  handleSearch()
}

// 处理待选表格选择变化
function handleAvailableSelectionChange(selection) {
  availableSelection.value = selection
}

// 处理已选表格选择变化
function handleSelectedSelectionChange(selection) {
  selectedSelection.value = selection
}

// 添加到已选
function addToSelected() {
  if (availableSelection.value.length === 0) {
    ElMessage.warning('请先选择要添加的项目')
    return
  }

  // 添加到已选列表，并添加备注字段
  const itemsToAdd = availableSelection.value.map(item => ({
    ...item,
    remark: ''
  }))

  selectedList.value.push(...itemsToAdd)

  //去除重复选项
  selectedList.value = selectedList.value.filter((item, index, self) =>
    index === self.findIndex(t => t.id === item.id)
  )


  // 从待选列表中移除
  const addedIds = availableSelection.value.map(item => item.id)
  availableList.value = availableList.value.filter(item => !addedIds.includes(item.id))

  // 清空选择
  availableSelection.value = []
  if (availableTableRef.value) {
    availableTableRef.value.clearSelection()
  }

  ElMessage.success(`已添加 ${itemsToAdd.length} 个项目`)
}

// 从已选中移除
function removeFromSelected() {
  if (selectedSelection.value.length === 0) {
    ElMessage.warning('请先选择要移除的项目')
    return
  }

  // 从已选列表中移除
  const removedIds = selectedSelection.value.map(item => item.id)
  const removedItems = selectedList.value.filter(item => removedIds.includes(item.id))
  selectedList.value = selectedList.value.filter(item => !removedIds.includes(item.id))

  // 添加回待选列表（如果当前页面包含这些项目）
  // 这里简化处理，直接重新加载当前页
  loadAvailableItems()

  // 清空选择
  selectedSelection.value = []
  if (selectedTableRef.value) {
    selectedTableRef.value.clearSelection()
  }

  ElMessage.success(`已移除 ${removedItems.length} 个项目`)
}

// 处理确认
function handleConfirm() {
  console.log('临时新增检测项目:', selectedList.value)
  emit('confirm', [...selectedList.value])
  handleClose()
}

// 处理关闭
function handleClose() {
  dialogVisible.value = false
  emit('update:visible', false)
}

// 筛选方法
const filterByColumn = (column) => {
  return (value, row) => {
    if (!value) return true;
    return String(row[column]).includes(value);
  };
};

// 计算各列的筛选选项
const getFilterOptions = (column) => {
  return computed(() => {
    const allValues = [...availableList.value, ...selectedList.value]
      .map(item => item[column])
      .filter((value, index, self) => self.indexOf(value) === index && value);
    return [{ text: '全部', value: '' }, ...allValues.map(value => ({ text: value, value }))];
  });
};

// 初始化筛选选项
const parameterFilters = getFilterOptions('parameter');
const methodFilters = getFilterOptions('method');
const qualificationCodeFilters = getFilterOptions('qualificationCode');
const limitationScopeFilters = getFilterOptions('limitationScope');
const remarkFilters = getFilterOptions('remark');

// 监听数据变化，更新筛选选项
watch([availableList, selectedList], () => {
  // 强制更新筛选选项
  parameterFilters.value;
  methodFilters.value;
  qualificationCodeFilters.value;
  limitationScopeFilters.value;
  remarkFilters.value;
});

// 防抖查询函数
const debounceSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    handleSearch()
  }, 500) // 500ms防抖
}

// 处理方法输入
const handleMethodInput = () => {
  debounceSearch()
}

// 处理参数输入
const handleParameterInput = () => {
  debounceSearch()
}

// 处理清空
const handleClear = () => {
  // 重置到第一页
  currentPage.value = 1
  handleSearch()
}

// 重置查询条件
const handleReset = () => {
  searchMethod.value = ''
  searchParameter.value = ''
  currentPage.value = 1
  handleSearch()
}

// 主查询函数
const handleSearch = async () => {
  try {
    searchLoading.value = true
    await loadAvailableItems({
      method: searchMethod.value,
      parameter: searchParameter.value,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      category: props.category
    })
  } catch (error) {
    ElMessage.error('查询失败，请重试')
  } finally {
    searchLoading.value = false
  }
}
</script>

<style scoped>
.test-item-selector {
  width: 100%;
}

.selector-content {
  padding: 10px 0;
}

.info-bar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.category-info {
  margin-left: 30px;
  font-weight: bold;
  color: #303133;
}

.count-info {
  margin-left: 100px;
  color: #606266;
  font-size: 14px;
}

.selector-main {
  display: flex;
  gap: 15px;
  /* height: 500px; */
}

.left-panel,
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.center-panel {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 60px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.panel-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 表格样式调整 */
/* :deep(.el-table) {
  border: 1px solid #ebeef5;
}

:deep(.el-table th) {
  background-color: #fafafa;
} */
</style>