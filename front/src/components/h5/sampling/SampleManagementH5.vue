<template>
  <div class="sample-management-h5">
    <!-- 统计信息 -->
    <div class="section">
      <h3 class="section-title">样品统计</h3>
      <div class="statistics-grid">
        <div class="stat-item">
          <div class="stat-number">{{ sampleStatistics.totalCount }}</div>
          <div class="stat-label">总数量</div>
        </div>
        <div class="stat-item pending">
          <div class="stat-number">{{ sampleStatistics.pendingCount }}</div>
          <div class="stat-label">待采集</div>
        </div>
        <div class="stat-item collected">
          <div class="stat-number">{{ sampleStatistics.collectedCount }}</div>
          <div class="stat-label">已采集</div>
        </div>
        <div class="stat-item submitted">
          <div class="stat-number">{{ sampleStatistics.submittedCount }}</div>
          <div class="stat-label">已送检</div>
        </div>
        <div class="stat-item testing" v-if="sampleStatistics.testingCount > 0">
          <div class="stat-number">{{ sampleStatistics.testingCount }}</div>
          <div class="stat-label">测试中</div>
        </div>
        <div class="stat-item completed" v-if="sampleStatistics.completedCount > 0">
          <div class="stat-number">{{ sampleStatistics.completedCount }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <!-- 样品记录列表 -->
    <div class="section">
      <h3 class="section-title">样品记录</h3>
      <div v-if="sampleRecords.length === 0" class="empty-state">
        <el-icon><Document /></el-icon>
        <p>暂无样品记录</p>
        <p class="empty-tip">请确保网络连接正常，或联系管理员</p>
      </div>
      <div v-else class="sample-list">
        <div 
          v-for="(sample, index) in sampleRecords" 
          :key="sample.id"
          class="sample-item"
        >
          <div class="sample-header">
            <div class="sample-number">样品序号：{{ sample.sampleNumber || (index + 1) }}</div>
            <div class="sample-status" :class="getSampleStatusClass(sample.status)">
              {{ getSampleStatusLabel(sample.status) }}
            </div>
          </div>
          
          <div class="sample-info">
            <div class="info-row">
              <span class="label">样品类型：</span>
              <span class="value">{{ sample.sampleType || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">样品来源：</span>
              <span class="value">{{ sample.sampleSource || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">点位名称：</span>
              <span class="value">{{ sample.pointName || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">采样周期：</span>
              <span class="value">{{ sample.cycleNumber }}({{ sample.cycleType }})</span>
            </div>
            <div class="info-row">
              <span class="label">检测类别：</span>
              <span class="value">{{ sample.detectionCategory || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">检测参数：</span>
              <span class="value">{{ sample.detectionParameter || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">检测方法：</span>
              <span class="value">{{ sample.detectionMethod || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">采集时间：</span>
              <span class="value">{{ formatDateTime(sample.collectionTime) || '-' }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="sample-actions">
            <el-button 
              v-if="sample.status === 0"
              type="primary" 
              size="small"
              @click="handleSampleCollect(sample)"
            >
              采集
            </el-button>
            <el-button 
              v-if="sample.status === 1"
              type="warning" 
              size="small"
              @click="handleSampleSubmit(sample)"
            >
              送检
            </el-button>
          </div>

          <!-- 瓶组信息 -->
          <div v-if="sample.bottleGroups && sample.bottleGroups.length > 0" class="bottle-groups">
            <div class="bottle-groups-title">关联瓶组</div>
            <div class="bottle-list">
              <div
                v-for="bottle in sample.bottleGroups"
                :key="bottle.id"
                class="bottle-item"
              >
                <div class="bottle-info">
                  <div class="bottle-name">{{ bottle.bottleGroupCode || `瓶组${bottle.id}` }}</div>
                  <div class="bottle-status" :class="getBottleStatusClass(bottle.status)">
                    {{ getBottleStatusLabel(bottle.status) }}
                  </div>
                </div>
                <div class="bottle-details">
                  <div class="bottle-detail-row">
                    <span class="detail-label">类型：</span>
                    <span class="detail-value">{{ bottle.bottleType || '默认瓶组' }}</span>
                  </div>
                  <div class="bottle-detail-row">
                    <span class="detail-label">容量：</span>
                    <span class="detail-value">{{ bottle.bottleVolume || '-' }}</span>
                  </div>
                  <div class="bottle-detail-row">
                    <span class="detail-label">存储：</span>
                    <span class="detail-value">
                      {{ bottle.storageStyles && bottle.storageStyles.length > 0 ? bottle.storageStyles.join(', ') : '-' }}
                    </span>
                  </div>
                  <div class="bottle-detail-row">
                    <span class="detail-label">时效：</span>
                    <span class="detail-value">
                      {{ bottle.sampleAge ? `${bottle.sampleAge}${bottle.sampleAgeUnit}` : '-' }}
                    </span>
                  </div>
                  <div class="bottle-detail-row">
                    <span class="detail-label">检测方法：</span>
                    <span class="detail-value">{{ bottle.detectionMethod || '-' }}</span>
                  </div>
                </div>
                <div class="bottle-actions">
                  <el-button
                    v-if="bottle.status === 0"
                    type="primary"
                    size="small"
                    @click="handleBottleCollect(bottle)"
                  >
                    采集
                  </el-button>
                  <el-button
                    v-if="bottle.status === 1"
                    type="warning"
                    size="small"
                    @click="handleBottleSubmit(bottle)"
                  >
                    送检
                  </el-button>
                  <el-button
                    type="info"
                    size="small"
                    @click="handleBottlePrint(bottle)"
                  >
                    打印
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 展开瓶组按钮 -->
          <div v-else-if="!sample.bottleGroupsLoaded" class="load-bottles">
            <el-button
              type="text"
              size="small"
              @click="loadBottleGroups(sample)"
              :loading="sample.bottleGroupsLoading"
            >
              <el-icon><Box /></el-icon>
              查看瓶组信息
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 打印弹窗 -->
    <SampleLabelPrintH5
      v-model="printDialogVisible"
      :bottle-group="currentPrintBottle"
      :task-info="currentPrintTaskInfo"
    />
  </div>
</template>

<script setup>
import { getCurrentInstance, nextTick, ref } from 'vue'
import { Document, Box } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { updateSampleRecordStatus } from '@/api/sampling/sampleRecord'
import { getBottleGroupsBySample, updateBottleGroupStatus } from '@/api/sampling/bottleGroup'
import { parseTime } from '@/utils/ruoyi'
import SampleLabelPrintH5 from './SampleLabelPrintH5.vue'

// Props
const props = defineProps({
  taskData: {
    type: Object,
    default: () => ({})
  },
  sampleRecords: {
    type: Array,
    default: () => []
  },
  sampleStatistics: {
    type: Object,
    default: () => ({
      totalCount: 0,
      pendingCount: 0,
      collectedCount: 0,
      submittedCount: 0
    })
  }
})

// Emits
const emit = defineEmits(['refresh'])

const { proxy } = getCurrentInstance()

// 打印相关数据
const printDialogVisible = ref(false)
const currentPrintBottle = ref({})
const currentPrintTaskInfo = ref({})

// 方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}')
}

const getSampleStatusClass = (status) => {
  switch (status) {
    case 0: return 'status-pending'
    case 1: return 'status-collected'
    case 2: return 'status-submitted'
    case 3: return 'status-testing'
    case 4: return 'status-completed'
    default: return ''
  }
}

const getSampleStatusLabel = (status) => {
  switch (status) {
    case 0: return '待采集'
    case 1: return '已采集'
    case 2: return '已送检'
    case 3: return '测试中'
    case 4: return '已完成'
    default: return '未知'
  }
}

const getBottleStatusClass = (status) => {
  switch (status) {
    case 0: return 'bottle-pending'
    case 1: return 'bottle-collected'
    case 2: return 'bottle-submitted'
    default: return ''
  }
}

const getBottleStatusLabel = (status) => {
  switch (status) {
    case 0: return '待采集'
    case 1: return '已采集'
    case 2: return '已送检'
    default: return '未知'
  }
}

const handleSampleCollect = async (sample) => {
  try {
    await updateSampleRecordStatus(sample.id, 1)
    ElMessage.success('样品采集成功')
    emit('refresh')
  } catch (error) {
    console.error('样品采集失败:', error)
    ElMessage.error('样品采集失败')
  }
}

const handleSampleSubmit = async (sample) => {
  try {
    await updateSampleRecordStatus(sample.id, 2)
    ElMessage.success('样品送检成功')
    emit('refresh')
  } catch (error) {
    console.error('样品送检失败:', error)
    ElMessage.error('样品送检失败')
  }
}

// 加载瓶组信息
const loadBottleGroups = async (sample) => {
  try {
    sample.bottleGroupsLoading = true
    const response = await getBottleGroupsBySample(sample.id)

    if (response.code === 200) {
      sample.bottleGroups = response.data || []
      sample.bottleGroupsLoaded = true
    } else {
      sample.bottleGroups = []
    }

    await nextTick()
  } catch (error) {
    console.error('加载瓶组信息失败:', error)
    ElMessage.error('加载瓶组信息失败，请检查网络连接')
    sample.bottleGroups = []
  } finally {
    sample.bottleGroupsLoading = false
  }
}

// 瓶组采集
const handleBottleCollect = async (bottle) => {
  try {
    await updateBottleGroupStatus(bottle.id, 1)
    ElMessage.success('瓶组采集成功')
    bottle.status = 1
    emit('refresh')
  } catch (error) {
    console.error('瓶组采集失败:', error)
    ElMessage.error('瓶组采集失败')
  }
}

// 瓶组送检
const handleBottleSubmit = async (bottle) => {
  try {
    await updateBottleGroupStatus(bottle.id, 2)
    ElMessage.success('瓶组送检成功')
    bottle.status = 2
    emit('refresh')
  } catch (error) {
    console.error('瓶组送检失败:', error)
    ElMessage.error('瓶组送检失败')
  }
}

// 瓶组打印
const handleBottlePrint = (bottle) => {
  // 找到当前瓶组所属的样品记录
  let sampleIndex = 0
  let totalSamples = props.sampleRecords.length

  // 遍历样品记录，找到包含当前瓶组的样品
  for (let i = 0; i < props.sampleRecords.length; i++) {
    const record = props.sampleRecords[i]
    if (record.bottleGroups && record.bottleGroups.some(b => b.id === bottle.id)) {
      sampleIndex = i + 1 // 样品序号从1开始
      break
    }
  }

  // 为瓶组添加样品序号信息
  const bottleWithIndex = {
    ...bottle,
    sortOrder: sampleIndex,
    totalCount: totalSamples
  }

  // 为任务信息添加样品总数
  const taskInfoWithCount = {
    ...props.taskData,
    totalSamples: totalSamples,
    sampleCount: totalSamples
  }

  // 显示打印弹窗
  currentPrintBottle.value = bottleWithIndex
  currentPrintTaskInfo.value = taskInfoWithCount
  printDialogVisible.value = true
}
</script>

<style scoped>
.sample-management-h5 {
  padding: 15px;
}

.section {
  background: white;
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  background: #f8f9fa;
  margin: 0;
  padding: 12px 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e9ecef;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1px;
  background: #e9ecef;
}

.stat-item {
  background: white;
  padding: 15px 10px;
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-item.pending .stat-number {
  color: #fa8c16;
}

.stat-item.collected .stat-number {
  color: #1890ff;
}

.stat-item.submitted .stat-number {
  color: #52c41a;
}

.stat-item.testing .stat-number {
  color: #fa8c16;
}

.stat-item.completed .stat-number {
  color: #1890ff;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #999;
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.sample-list {
  padding: 15px;
}

.sample-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.sample-item:last-child {
  margin-bottom: 0;
}

.sample-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.sample-number {
  font-weight: 500;
  color: #333;
}

.sample-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-collected {
  background: #f0f9ff;
  color: #1890ff;
}

.status-submitted {
  background: #f6ffed;
  color: #52c41a;
}

.status-testing {
  background: #fff2e8;
  color: #fa8c16;
}

.status-completed {
  background: #f0f9ff;
  color: #1890ff;
}

.sample-info {
  padding: 12px 15px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  color: #666;
  min-width: 80px;
  margin-right: 8px;
}

.info-row .value {
  color: #333;
  word-break: break-all;
}

.sample-actions {
  padding: 12px 15px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.bottle-groups {
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.bottle-groups-title {
  padding: 8px 15px;
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.bottle-list {
  padding: 0 15px 12px;
}

.bottle-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.bottle-item:last-child {
  margin-bottom: 0;
}

.bottle-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.bottle-name {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.bottle-status {
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.bottle-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.bottle-collected {
  background: #f0f9ff;
  color: #1890ff;
}

.bottle-submitted {
  background: #f6ffed;
  color: #52c41a;
}

.bottle-details {
  font-size: 12px;
  color: #666;
}

.bottle-details {
  margin-top: 8px;
}

.bottle-detail-row {
  display: flex;
  margin-bottom: 4px;
  font-size: 12px;
}

.bottle-detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #666;
  min-width: 60px;
  margin-right: 8px;
}

.detail-value {
  color: #333;
  word-break: break-all;
  flex: 1;
}

.bottle-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.bottle-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.load-bottles {
  padding: 12px 15px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.load-bottles .el-button {
  color: #409eff;
}

.load-bottles .el-icon {
  margin-right: 4px;
}
</style>
