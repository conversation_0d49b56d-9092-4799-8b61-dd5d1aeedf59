<template>
  <div class="qr-scanner">
    <div class="scanner-container">
      <video ref="videoRef" class="scanner-video" autoplay playsinline></video>
      <div class="scanner-overlay">
        <div class="scanner-frame"></div>
        <div class="scanner-tips">将二维码放入框内进行扫描</div>
      </div>
    </div>
    
    <div class="scanner-controls">
      <el-button @click="startScan" :disabled="scanning" type="primary">
        <el-icon><Search /></el-icon>
        {{ scanning ? '扫描中...' : '开始扫描' }}
      </el-button>
      <el-button @click="stopScan" :disabled="!scanning">
        <el-icon><Close /></el-icon>
        停止扫描
      </el-button>
      <el-button @click="switchCamera" :disabled="!scanning">
        <el-icon><RefreshRight /></el-icon>
        切换摄像头
      </el-button>
    </div>
    
    <div v-if="result" class="scan-result">
      <h4>扫描结果：</h4>
      <p>{{ result }}</p>
      <el-button type="success" @click="handleResult">确认</el-button>
      <el-button @click="clearResult">重新扫描</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Search, Close, RefreshRight } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  autoStart: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['scan-result', 'scan-error'])

// 响应式数据
const videoRef = ref(null)
const scanning = ref(false)
const result = ref('')
const stream = ref(null)
const currentFacingMode = ref('environment') // 'user' 前置, 'environment' 后置

// 方法
const startScan = async () => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('浏览器不支持摄像头访问')
    }

    const constraints = {
      video: {
        facingMode: currentFacingMode.value,
        width: { ideal: 1280 },
        height: { ideal: 720 }
      }
    }

    stream.value = await navigator.mediaDevices.getUserMedia(constraints)
    videoRef.value.srcObject = stream.value
    
    scanning.value = true
    startDecoding()
    
  } catch (error) {
    console.error('启动扫描失败:', error)
    emit('scan-error', error.message)
  }
}

const stopScan = () => {
  if (stream.value) {
    stream.value.getTracks().forEach(track => track.stop())
    stream.value = null
  }
  
  if (videoRef.value) {
    videoRef.value.srcObject = null
  }
  
  scanning.value = false
}

const switchCamera = async () => {
  if (!scanning.value) return
  
  stopScan()
  currentFacingMode.value = currentFacingMode.value === 'user' ? 'environment' : 'user'
  await startScan()
}

const startDecoding = () => {
  if (!scanning.value || !videoRef.value) return
  
  // 这里应该集成二维码解码库，如 jsQR
  // 由于没有安装相关库，这里使用模拟实现
  const checkForQRCode = () => {
    if (!scanning.value) return
    
    // 模拟扫描结果（实际应该使用 jsQR 等库进行解码）
    // const canvas = document.createElement('canvas')
    // const context = canvas.getContext('2d')
    // canvas.width = videoRef.value.videoWidth
    // canvas.height = videoRef.value.videoHeight
    // context.drawImage(videoRef.value, 0, 0, canvas.width, canvas.height)
    // const imageData = context.getImageData(0, 0, canvas.width, canvas.height)
    // const code = jsQR(imageData.data, imageData.width, imageData.height)
    
    // if (code) {
    //   result.value = code.data
    //   stopScan()
    //   emit('scan-result', code.data)
    //   return
    // }
    
    requestAnimationFrame(checkForQRCode)
  }
  
  requestAnimationFrame(checkForQRCode)
}

const handleResult = () => {
  emit('scan-result', result.value)
  clearResult()
}

const clearResult = () => {
  result.value = ''
}

// 生命周期
onMounted(() => {
  if (props.autoStart) {
    startScan()
  }
})

onUnmounted(() => {
  stopScan()
})
</script>

<style scoped>
.qr-scanner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.scanner-container {
  position: relative;
  flex: 1;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scanner-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.scanner-frame {
  width: 250px;
  height: 250px;
  border: 2px solid #409EFF;
  border-radius: 8px;
  position: relative;
}

.scanner-frame::before,
.scanner-frame::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #409EFF;
}

.scanner-frame::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.scanner-frame::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

.scanner-tips {
  color: white;
  font-size: 14px;
  margin-top: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 4px;
}

.scanner-controls {
  padding: 20px;
  background: white;
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.scanner-controls .el-button {
  flex: 1;
  min-width: 100px;
}

.scan-result {
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.scan-result h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.scan-result p {
  margin: 0 0 15px 0;
  padding: 10px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  word-break: break-all;
  font-family: monospace;
}

.scan-result .el-button {
  margin-right: 10px;
}
</style>
