import request from '@/utils/request'

// 获取瓶组管理列表
export function getBottleMaintenanceList(params) {
  return request({
    url: '/bottle-maintenance/list',
    method: 'get',
    params
  })
}

// 获取瓶组管理详情
export function getBottleMaintenanceDetail(id) {
  return request({
    url: `/bottle-maintenance/${id}`,
    method: 'get'
  })
}

// 添加瓶组管理
export function addBottleMaintenance(data) {
  return request({
    url: '/bottle-maintenance/add',
    method: 'post',
    data
  })
}

// 编辑瓶组管理
export function editBottleMaintenance(data) {
  return request({
    url: '/bottle-maintenance/edit',
    method: 'put',
    data
  })
}

// 删除瓶组管理
export function deleteBottleMaintenance(id) {
  return request({
    url: '/bottle-maintenance/delete',
    method: 'delete',
    params: { bottle_maintenance_id: id }
  })
}

// 批量导入瓶组管理
export function batchImportBottleMaintenance(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/bottle-maintenance/batch-import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载导入模板
export function downloadImportTemplate() {
  return request({
    url: '/bottle-maintenance/template/download',
    method: 'get',
    responseType: 'blob'
  })
}
