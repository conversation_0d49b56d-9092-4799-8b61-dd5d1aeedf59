import request from '@/utils/request'

// 获取任务的分组列表
export function getTaskGroups(taskId) {
  return request({
    url: `/sampling/task-group/task/${taskId}`,
    method: 'get'
  })
}

// 获取分组详情
export function getGroupDetail(groupId) {
  return request({
    url: `/sampling/task-group/${groupId}`,
    method: 'get'
  })
}

// 为单个分组分配执行人
export function assignUsersToGroup(groupId, data) {
  return request({
    url: `/sampling/task-group/${groupId}/assign-users`,
    method: 'put',
    data: data
  })
}

// 获取用户的分组任务
export function getUserGroups(userId) {
  return request({
    url: `/sampling/task-group/user/${userId}`,
    method: 'get'
  })
}

// 获取所有分组任务（管理员权限）
export function getAllGroups(query) {
  return request({
    url: '/sampling/task-group/list',
    method: 'get',
    params: query
  })
}

// 更新分组状态
export function updateGroupStatus(groupId, status) {
  return request({
    url: `/sampling/task-group/${groupId}/status`,
    method: 'put',
    data: status
  })
}

// 批量分配执行人（原有接口，保持兼容）
export function assignExecutorsToGroups(data) {
  return request({
    url: '/sampling/task-group/assign',
    method: 'post',
    data: data
  })
}
