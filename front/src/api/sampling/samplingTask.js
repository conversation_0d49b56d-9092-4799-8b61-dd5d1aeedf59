import request from '@/utils/request'

// 查询采样任务列表
export function listSamplingTask(query) {
  return request({
    url: '/sampling/task/page',
    method: 'get',
    params: query
  })
}

// 分页查询采样任务
export function pageSamplingTask(query) {
  return request({
    url: '/sampling/task/page',
    method: 'get',
    params: query
  })
}

// 增强版分页查询采样任务（参考项目报价列表）
export function pageSamplingTaskEnhanced(query) {
  return request({
    url: '/sampling/task/page-enhanced',
    method: 'get',
    params: query
  })
}

// 查询采样任务详细
export function getSamplingTask(taskId) {
  return request({
    url: '/sampling/task/get/' + taskId,
    method: 'get'
  })
}

// 新增采样任务
export function addSamplingTask(data) {
  return request({
    url: '/sampling/task/create',
    method: 'post',
    data: data
  })
}

// 修改采样任务
export function updateSamplingTask(data) {
  return request({
    url: '/sampling/task/update/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除采样任务
export function delSamplingTask(taskId) {
  return request({
    url: '/sampling/task/delete/' + taskId,
    method: 'delete'
  })
}

// 获取任务组员列表
export function getTaskMembers(taskId) {
  return request({
    url: '/sampling/task/members/' + taskId,
    method: 'get'
  })
}

// 根据项目报价ID查询采样任务
export function getTasksByProjectQuotationId(projectQuotationId) {
  return request({
    url: '/sampling/task/project/' + projectQuotationId,
    method: 'get'
  })
}

// 根据负责人用户ID获取采样任务列表
export function getTasksByResponsibleUserId(userId) {
  return request({
    url: `/sampling/task/user/${userId}`,
    method: 'get'
  })
}

// 创建采样任务分配
export function createSamplingTaskAssignment(data) {
  return request({
    url: '/sampling/task/assignment',
    method: 'post',
    data: data
  })
}



// 更新采样任务状态
export function updateSamplingTaskStatus(taskId, status) {
  return request({
    url: '/sampling/task/status/' + taskId,
    method: 'put',
    data: { status: status }
  })
}

// 设置采样任务加急状态
export function setSamplingTaskUrgent(taskId, isUrgent) {
  return request({
    url: `/sampling/task/urgent/${taskId}`,
    method: 'put',
    data: isUrgent
  })
}

// 创建采样任务
export function createSamplingTask(data) {
  return request({
    url: '/sampling/task/create',
    method: 'post',
    data: data
  })
}