# 设备管理二维码功能安装说明

## 📦 安装依赖

在前端项目根目录下运行以下命令安装vue-qrcode依赖：

```bash
npm install vue-qrcode@^2.0.0
```

或者使用yarn：

```bash
yarn add vue-qrcode@^2.0.0
```

## 🚀 功能说明

### 1. 二维码显示
- 在设备详情页面右上角自动显示二维码
- 二维码基于当前URL和设备ID生成，确保每次查看都完全相同
- 二维码包含跳转到H5页面的完整URL

### 2. H5页面
- 扫描二维码后跳转到专门的移动端设备详情页面
- 页面排版参考PC端设备详情，针对移动端优化
- 包含完整的设备信息展示

### 3. 响应式设计
- 支持不同屏幕尺寸的设备
- 移动端友好的界面设计
- 自适应布局

## 📱 使用方法

1. **查看二维码**：在设备管理页面点击"查看"按钮，在弹出的设备详情对话框右上角可以看到二维码

2. **扫描二维码**：使用手机扫描二维码，会自动跳转到H5版本的设备详情页面

3. **查看详情**：在H5页面可以查看完整的设备信息，包括：
   - 基础信息（设备编号、名称、类型等）
   - 校准/溯源管理信息
   - 管理责任信息
   - 财务信息
   - 附件信息（如果有）

## 🔧 技术实现

### 前端技术栈
- Vue 3 + Composition API
- Element Plus UI组件库
- @chenfengyuan/vue-qrcode 二维码生成
- Vue Router 路由管理

### 文件结构
```
front/src/
├── views/
│   ├── basedata/equipmentManage/components/
│   │   └── EquipmentDetail.vue          # 设备详情组件（包含二维码）
│   └── h5/
│       └── EquipmentDetailH5.vue        # H5设备详情页面
├── api/basedata/
│   └── equipmentManagement.js           # API接口
└── router/
    └── index.js                         # 路由配置
```

### 二维码URL格式
```
{当前域名}/equipment-detail-h5?id={设备ID}
```

例如：
- 开发环境：`http://localhost:3000/equipment-detail-h5?id=123`
- 生产环境：`https://yourdomain.com/equipment-detail-h5?id=123`

## 🎨 样式特点

### PC端二维码显示
- 位置：设备详情对话框右上角
- 样式：白色背景，圆角边框，阴影效果
- 尺寸：150x150像素
- 包含标题和提示文字

### H5页面设计
- 渐变色头部设计
- 卡片式信息展示
- 移动端优化的字体和间距
- 状态标签彩色显示
- 响应式布局适配

## 🔍 故障排除

### 1. 二维码不显示
- 检查是否安装了vue-qrcode依赖
- 确认设备数据中包含id字段
- 检查浏览器控制台是否有错误信息

### 2. 扫码后页面404
- 确认路由配置是否正确
- 检查H5页面文件是否存在
- 验证URL参数是否正确传递

### 3. H5页面数据不显示
- 检查API接口是否正常
- 确认设备ID参数是否正确
- 查看网络请求是否成功

### 4. 移动端样式问题
- 检查CSS媒体查询是否生效
- 确认viewport设置是否正确
- 验证响应式样式是否加载

## 📝 注意事项

1. **依赖安装**：必须先安装vue-qrcode依赖才能正常使用
2. **设备ID**：确保设备数据中包含有效的id字段
3. **网络访问**：H5页面需要网络访问，确保API接口可正常调用
4. **浏览器兼容**：建议使用现代浏览器以获得最佳体验
5. **HTTPS**：生产环境建议使用HTTPS协议以确保安全性

## 🎯 功能扩展

如需扩展功能，可以考虑：

1. **二维码样式定制**：修改qrCodeOptions配置
2. **H5页面功能增强**：添加更多交互功能
3. **数据缓存**：添加本地存储减少网络请求
4. **分享功能**：添加分享到社交媒体的功能
5. **打印功能**：添加二维码打印功能

## 📞 技术支持

如遇到问题，请检查：
1. 依赖是否正确安装
2. 代码是否按照说明正确配置
3. 浏览器控制台是否有错误信息
4. 网络请求是否正常
