<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API超时测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #337ecc;
        }
        .test-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #409eff;
            color: #409eff;
        }
        .error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .loading {
            background-color: #fdf6ec;
            border: 1px solid #e6a23c;
            color: #e6a23c;
        }
    </style>
</head>
<body>
    <h1>🧪 API超时测试</h1>
    
    <div class="test-section">
        <h3>📋 样品记录生成API测试</h3>
        <p>测试 generateSampleRecordsForGroup API 的超时设置</p>
        <button class="test-button" onclick="testGenerateSampleRecords()">测试样品记录生成</button>
        <div id="sampleResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>🍶 瓶组生成API测试</h3>
        <p>测试 generateBottleGroups API 的超时设置</p>
        <button class="test-button" onclick="testGenerateBottleGroups()">测试瓶组生成</button>
        <div id="bottleResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>📊 测试结果统计</h3>
        <div id="statistics">
            <p>成功: <span id="successCount">0</span></p>
            <p>失败: <span id="errorCount">0</span></p>
            <p>超时: <span id="timeoutCount">0</span></p>
        </div>
    </div>

    <script>
        let successCount = 0;
        let errorCount = 0;
        let timeoutCount = 0;

        function updateStatistics() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('errorCount').textContent = errorCount;
            document.getElementById('timeoutCount').textContent = timeoutCount;
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        async function testGenerateSampleRecords() {
            const button = event.target;
            button.disabled = true;
            button.textContent = '测试中...';
            
            showResult('sampleResult', '正在测试样品记录生成API...', 'loading');
            
            try {
                const startTime = Date.now();
                
                // 模拟API调用
                const response = await fetch('/sampling/sample-records/generate/group/1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test_token'
                    },
                    signal: AbortSignal.timeout(30000) // 30秒超时
                });
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('sampleResult', `✅ 成功！耗时: ${duration}ms`, 'success');
                    successCount++;
                } else {
                    showResult('sampleResult', `❌ 失败！状态码: ${response.status}`, 'error');
                    errorCount++;
                }
                
            } catch (error) {
                if (error.name === 'TimeoutError') {
                    showResult('sampleResult', '⏰ 请求超时（30秒）', 'error');
                    timeoutCount++;
                } else {
                    showResult('sampleResult', `❌ 错误: ${error.message}`, 'error');
                    errorCount++;
                }
            } finally {
                button.disabled = false;
                button.textContent = '测试样品记录生成';
                updateStatistics();
            }
        }

        async function testGenerateBottleGroups() {
            const button = event.target;
            button.disabled = true;
            button.textContent = '测试中...';
            
            showResult('bottleResult', '正在测试瓶组生成API...', 'loading');
            
            try {
                const startTime = Date.now();
                
                // 模拟API调用
                const response = await fetch('/sampling/bottle-groups/generate/1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test_token'
                    },
                    signal: AbortSignal.timeout(30000) // 30秒超时
                });
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('bottleResult', `✅ 成功！耗时: ${duration}ms`, 'success');
                    successCount++;
                } else {
                    showResult('bottleResult', `❌ 失败！状态码: ${response.status}`, 'error');
                    errorCount++;
                }
                
            } catch (error) {
                if (error.name === 'TimeoutError') {
                    showResult('bottleResult', '⏰ 请求超时（30秒）', 'error');
                    timeoutCount++;
                } else {
                    showResult('bottleResult', `❌ 错误: ${error.message}`, 'error');
                    errorCount++;
                }
            } finally {
                button.disabled = false;
                button.textContent = '测试瓶组生成';
                updateStatistics();
            }
        }

        // 页面加载时显示当前配置
        window.onload = function() {
            console.log('🔧 API超时配置:');
            console.log('- 样品记录生成API: 30秒');
            console.log('- 瓶组生成API: 30秒');
            console.log('- 其他API: 10秒（默认）');
        };
    </script>
</body>
</html>
