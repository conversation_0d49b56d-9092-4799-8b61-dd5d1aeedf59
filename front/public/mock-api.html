<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5页面API模拟</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .api-title {
            font-weight: 600;
            color: #409EFF;
            margin-bottom: 10px;
        }
        .api-url {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 10px;
        }
        .api-response {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 12px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>H5页面API模拟数据</h1>
        
        <div class="note">
            <strong>说明：</strong>由于后端服务可能未启动或API接口未实现，H5页面可能无法正常显示数据。
            以下是模拟的API响应数据格式，供开发参考。
        </div>

        <div class="api-section">
            <div class="api-title">1. 获取任务分组详情</div>
            <div class="api-url">GET /sampling/task-groups/{id}</div>
            <div class="api-response">{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "taskCode": "241200010475",
    "taskName": "某公司废气检测任务",
    "projectName": "环境监测项目",
    "customerName": "某环保科技公司",
    "cycleNumber": 1,
    "cycleType": "周期1",
    "detectionCategory": "无组织废气",
    "pointName": "厂界下风向3",
    "status": 1,
    "samplingTaskId": 1
  }
}</div>
        </div>

        <div class="api-section">
            <div class="api-title">2. 获取样品记录列表</div>
            <div class="api-url">GET /sampling/sample-records/group/{groupId}</div>
            <div class="api-response">{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "sampleNumber": "S001",
      "samplingTime": "2024-12-31 10:00:00",
      "testItems": "臭气浓度",
      "sampleDescription": "厂界下风向采样点",
      "status": 0,
      "bottleGroups": null,
      "bottleGroupsLoading": false,
      "bottleGroupsLoaded": false
    },
    {
      "id": 2,
      "sampleNumber": "S002",
      "samplingTime": "2024-12-31 11:00:00",
      "testItems": "臭气浓度",
      "sampleDescription": "厂界上风向采样点",
      "status": 1,
      "bottleGroups": null,
      "bottleGroupsLoading": false,
      "bottleGroupsLoaded": false
    }
  ]
}</div>
        </div>

        <div class="api-section">
            <div class="api-title">3. 获取样品统计信息</div>
            <div class="api-url">GET /sampling/sample-records/statistics/group/{groupId}</div>
            <div class="api-response">{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCount": 4,
    "pendingCount": 2,
    "collectedCount": 1,
    "submittedCount": 1
  }
}</div>
        </div>

        <div class="api-section">
            <div class="api-title">4. 获取瓶组信息</div>
            <div class="api-url">GET /sampling/bottle-groups/sample/{sampleId}</div>
            <div class="api-response">{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "bottleGroupNumber": "BG001",
      "bottleType": "10L 气袋",
      "bottleVolume": "10L",
      "status": 0,
      "testItems": "臭气浓度",
      "detectionMethod": "三点比较式臭袋法"
    },
    {
      "id": 2,
      "bottleGroupNumber": "BG002",
      "bottleType": "5L 气袋",
      "bottleVolume": "5L",
      "status": 1,
      "testItems": "臭气浓度",
      "detectionMethod": "三点比较式臭袋法"
    }
  ]
}</div>
        </div>

        <div class="api-section">
            <div class="api-title">5. 获取点位信息</div>
            <div class="api-url">GET /sampling/point-info/group/{groupId}</div>
            <div class="api-response">{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "pointName": "厂界下风向3",
    "samplingTime": "2024-12-31 10:00:00",
    "longitude": "120.123456",
    "latitude": "30.654321",
    "altitude": "15",
    "weatherCondition": "晴",
    "temperature": "25",
    "humidity": "60",
    "windSpeed": "2.5",
    "windDirection": "东南",
    "pointPhotoUrl": "/uploads/point_photo_1.jpg",
    "eastPhotoUrl": "/uploads/east_1.jpg",
    "southPhotoUrl": "/uploads/south_1.jpg",
    "westPhotoUrl": "/uploads/west_1.jpg",
    "northPhotoUrl": "/uploads/north_1.jpg",
    "remarks": "采样点位于厂界围墙外2米处"
  }
}</div>
        </div>

        <div class="api-section">
            <div class="api-title">使用建议</div>
            <div class="api-response">1. 确保后端服务已启动 (python run.py)
2. 检查API接口是否已实现
3. 在浏览器开发者工具中查看网络请求
4. 使用模拟数据进行前端功能测试
5. 在移动设备或开发者工具的移动模式下测试H5页面</div>
        </div>
    </div>
</body>
</html>
