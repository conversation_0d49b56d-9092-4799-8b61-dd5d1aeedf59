<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H5页面测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-links {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .test-link {
            display: block;
            padding: 15px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            transition: transform 0.2s;
        }
        .test-link:hover {
            transform: translateY(-2px);
        }
        .description {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>H5页面测试</h1>
        <div class="test-links">
            <a href="/h5" class="test-link">
                H5首页
                <div class="description">LIMS采样管理系统移动端首页</div>
            </a>
            <a href="/execution-tasks-h5" class="test-link">
                执行任务H5页面
                <div class="description">查看和管理执行任务的移动端页面</div>
            </a>
            <a href="/sampling-task-h5?id=1" class="test-link">
                采样任务H5页面
                <div class="description">测试采样任务的移动端页面</div>
            </a>
            <a href="/task-detail-h5?id=1" class="test-link">
                任务详情H5页面
                <div class="description">查看任务详细信息的移动端页面</div>
            </a>
            <a href="/equipment-detail-h5?id=1" class="test-link">
                设备详情H5页面
                <div class="description">测试设备详情的移动端页面</div>
            </a>
            <a href="/mock-api.html" class="test-link">
                API模拟数据
                <div class="description">查看H5页面的API接口模拟数据</div>
            </a>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">使用说明：</h3>
            <ul style="margin: 0; padding-left: 20px; color: #666; font-size: 14px;">
                <li>点击上方链接可以测试对应的H5页面</li>
                <li>H5首页包含扫码采样、任务列表、离线数据等功能</li>
                <li>采样任务H5页面包含任务信息、样品管理、点位信息三个Tab</li>
                <li>点位信息支持图片上传和位置获取功能</li>
                <li>样品管理支持瓶组标签打印功能</li>
                <li>支持离线缓存和PWA功能</li>
                <li>请在移动设备或浏览器开发者工具的移动模式下测试</li>
            </ul>

            <h3 style="margin: 20px 0 10px 0; color: #333;">注意事项：</h3>
            <ul style="margin: 0; padding-left: 20px; color: #666; font-size: 14px;">
                <li><strong>后端服务：</strong>需要启动后端服务才能正常获取数据</li>
                <li><strong>API接口：</strong>H5页面使用与PC端相同的API接口</li>
                <li><strong>数据来源：</strong>所有数据来自真实的采样管理系统</li>
                <li><strong>离线功能：</strong>无网络时会显示缓存数据</li>
                <li><strong>错误处理：</strong>API失败时提供重试和PC端跳转选项</li>
            </ul>
        </div>
    </div>
</body>
</html>
