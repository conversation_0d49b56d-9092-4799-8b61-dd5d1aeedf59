#!/bin/bash

# LIMS 项目 Docker 快速部署脚本
# 作用：从项目根目录快速启动 Docker 部署

set -e

echo "===== LIMS 项目 Docker 快速部署 ====="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查 docker 目录是否存在
if [ ! -d "docker" ]; then
    echo "错误: docker 目录不存在，请确保在项目根目录下运行此脚本"
    exit 1
fi

# 进入 docker 目录
cd docker

log_info "进入 docker 目录: $(pwd)"

# 检查部署脚本是否存在
if [ ! -f "deploy.sh" ]; then
    echo "错误: docker/deploy.sh 不存在"
    exit 1
fi

# 给部署脚本执行权限
chmod +x deploy.sh

log_info "开始执行 Docker 部署..."

# 执行部署脚本
./deploy.sh

log_info "Docker 部署完成！"
log_warn "注意: 所有 Docker 相关操作都需要在 docker 目录下进行"
