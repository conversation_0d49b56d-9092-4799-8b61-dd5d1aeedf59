# LIMS 项目 Docker 部署

本项目支持使用 Docker 和 Docker Compose 进行容器化部署。所有 Docker 相关文件都位于 `docker/` 目录下。

## 📁 Docker 目录结构

```
docker/
├── README.md                   # 详细部署文档
├── deploy.sh                   # 一键部署脚本
├── docker-compose.yml          # 生产环境配置
├── docker-compose.dev.yml      # 开发环境配置
├── .env                        # 环境变量配置
├── backend/                    # 后端相关
│   ├── Dockerfile              # 后端生产环境镜像
│   ├── Dockerfile.dev          # 后端开发环境镜像
│   └── .dockerignore           # 后端构建忽略文件
├── frontend/                   # 前端相关
│   ├── Dockerfile              # 前端镜像
│   ├── nginx.conf              # 前端 Nginx 配置
│   └── .dockerignore           # 前端构建忽略文件
├── nginx/                      # 主 Nginx 配置
│   ├── nginx.conf              # 主配置文件
│   └── conf.d/
│       └── lims.conf           # LIMS 站点配置
├── mysql/                      # MySQL 配置
│   └── conf.d/
│       └── mysql.cnf           # MySQL 配置文件
└── ssl/                        # SSL 证书目录
```

## 🚀 快速开始

### 方式一：根目录快速部署
```bash
# 在项目根目录执行
./docker-deploy.sh
```

### 方式二：进入 docker 目录部署
```bash
# 进入 docker 目录
cd docker

# 一键部署
./deploy.sh

# 或手动部署
docker-compose up -d --build
```

## 🌐 访问地址

部署完成后：
- **前端应用**: http://localhost
- **后端API**: http://localhost/dev-api  
- **API文档**: http://localhost/dev-api/docs
- **默认账号**: admin / admin123

## 🔧 常用命令

**注意：以下命令需要在 `docker/` 目录下执行**

```bash
# 进入 docker 目录
cd docker

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]

# 停止所有服务
docker-compose down

# 开发环境
docker-compose -f docker-compose.dev.yml up -d --build
```

## 📋 服务说明

- **MySQL 8.0**: 主数据库，端口 3306
- **Redis 7**: 缓存服务，端口 6379
- **FastAPI**: 后端服务，端口 9099
- **Vue 3**: 前端应用，通过 Nginx 提供
- **Nginx**: 反向代理，端口 80/443

## 🌐 镜像源

项目使用 Xuanyuan 镜像源以提高下载速度：
- `docker.xuanyuan.me/python:3.12-slim`
- `docker.xuanyuan.me/mysql:8.0`
- `docker.xuanyuan.me/redis:7-alpine`
- `docker.xuanyuan.me/nginx:alpine`
- `docker.xuanyuan.me/node:18-alpine`

## 📖 详细文档

更多详细信息请查看：[docker/README.md](docker/README.md)

## 🔍 开发环境

如需开发环境（支持热重载）：
```bash
cd docker
docker-compose -f docker-compose.dev.yml up -d --build
```

## ⚠️ 重要提示

1. **所有 Docker 相关操作都需要在 `docker/` 目录下进行**
2. 确保已安装 Docker 和 Docker Compose
3. 首次部署可能需要较长时间下载镜像
4. 生产环境请修改默认密码和配置
