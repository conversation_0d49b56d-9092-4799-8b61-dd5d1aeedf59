# 样品记录功能实现说明

## 功能概述

样品记录管理功能是LIMS系统采样管理模块的重要组成部分，用于管理采样任务执行过程中的样品记录。该功能实现了从执行任务开始到样品完成检测的全流程管理。

## 核心需求

根据`docs/样品记录.md`中的需求，实现以下功能：

1. **自动生成样品记录**：在采样执行任务开始时，根据关联的周期条目和项目报价明细自动生成样品记录
2. **样品数量计算**：根据项目报价明细中的采样频次和样品数计算需要生成的样品记录数（频次 × 样品数的最大值）
3. **现场直读过滤**：排除技术手册中分析类型为`ON_SITE_CHECK`的明细，不生成样品记录
4. **样品管理界面**：在采样执行页面提供样品管理功能，支持查看、管理样品记录
5. **样品状态管理**：支持样品从待采集到已完成的全流程状态管理

## 技术架构

### 后端实现

#### 1. 数据模型层
- **SampleRecord**：样品记录实体模型
  - 包含样品序号、样品类型、采样周期、样品状态、采集时间、点位名称等字段
  - 关联采样任务执行指派、检测周期条目、项目报价明细

#### 2. 数据访问层
- **SampleRecordDAO**：样品记录数据访问对象
  - 提供CRUD操作和查询方法
  - 支持批量创建、状态更新、统计查询等功能

#### 3. 业务逻辑层
- **SampleRecordService**：样品记录服务
  - 实现样品记录生成逻辑
  - 处理现场直读类型过滤
  - 计算最大样品数量
  - 提供状态管理功能

#### 4. 控制器层
- **SampleRecordController**：样品记录API控制器
  - 提供RESTful API接口
  - 支持创建、查询、更新、删除操作
  - 提供统计信息接口

### 前端实现

#### 1. API接口层
- **sampleRecord.js**：样品记录API接口
  - 封装后端API调用
  - 提供统一的数据访问接口

#### 2. 页面组件层
- **采样执行页面增强**：
  - 添加样品管理按钮
  - 集成样品记录管理弹窗
  - 支持样品状态操作

## 核心算法

### 样品记录生成算法

```python
async def generate_sample_records_for_assignment(self, assignment_id: int, create_by: int):
    """为执行任务生成样品记录"""
    
    # 1. 获取执行任务信息
    assignment = await self._get_assignment_with_details(assignment_id)
    
    # 2. 获取关联的周期条目
    cycle_items = await self._get_cycle_items_for_assignment(assignment)
    
    # 3. 计算需要生成的样品记录数
    max_sample_count = await self._calculate_max_sample_count(cycle_items)
    
    # 4. 生成样品记录
    sample_records = []
    for i in range(max_sample_count):
        sample_number = i + 1
        for cycle_item in cycle_items:
            # 为每个周期条目生成样品记录
            sample_record = SampleRecord(...)
            sample_records.append(sample_record)
    
    # 5. 批量创建样品记录
    return await self.sample_record_dao.batch_create_sample_records(sample_records)
```

### 样品数量计算算法

```python
async def _calculate_max_sample_count(self, cycle_items: List[DetectionCycleItem]) -> int:
    """计算最大样品数量"""
    max_count = 0
    
    for cycle_item in cycle_items:
        quotation_item = cycle_item.project_quotation_item
        
        # 检查是否为现场直读类型，如果是则跳过
        if await self._is_on_site_check_item(quotation_item):
            continue
        
        # 计算该条目的样品数：频次 * 样品数
        frequency = quotation_item.frequency or 1
        sample_count = quotation_item.sample_count or 1
        item_sample_count = frequency * sample_count
        
        max_count = max(max_count, item_sample_count)
    
    return max_count
```

### 现场直读类型判断算法

```python
async def _is_on_site_check_item(self, quotation_item: ProjectQuotationItem) -> bool:
    """检查项目报价明细是否为现场直读类型"""
    
    # 优先通过资质唯一编号查询技术手册
    if quotation_item.qualification_code:
        stmt = select(TechnicalManual.analysis_type).where(
            TechnicalManual.qualification_code == quotation_item.qualification_code
        )
        result = await self.db.execute(stmt)
        analysis_type = result.scalar_one_or_none()
        return analysis_type == "ON_SITE_CHECK"
    
    # 如果没有资质编号，通过参数和方法查询
    stmt = select(TechnicalManual.analysis_type).where(
        and_(
            TechnicalManual.parameter == quotation_item.parameter,
            TechnicalManual.method == quotation_item.method
        )
    )
    result = await self.db.execute(stmt)
    analysis_type = result.scalar_one_or_none()
    return analysis_type == "ON_SITE_CHECK"
```

## 数据库设计

### 样品记录表结构

```sql
CREATE TABLE `sample_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_assignment_id` bigint NOT NULL COMMENT '采样任务执行指派ID',
  `detection_cycle_item_id` bigint NOT NULL COMMENT '检测周期条目ID',
  `project_quotation_item_id` int NOT NULL COMMENT '项目报价明细ID',
  `sample_number` int NOT NULL COMMENT '样品序号（从1开始）',
  `sample_type` varchar(50) DEFAULT NULL COMMENT '样品类型',
  `sample_source` varchar(50) DEFAULT NULL COMMENT '样品来源',
  `point_name` varchar(200) DEFAULT NULL COMMENT '点位名称',
  `cycle_number` int NOT NULL COMMENT '周期序号',
  `cycle_type` varchar(50) DEFAULT NULL COMMENT '周期类型',
  `detection_category` varchar(100) DEFAULT NULL COMMENT '检测类别',
  `detection_parameter` varchar(100) DEFAULT NULL COMMENT '检测参数',
  `detection_method` varchar(200) DEFAULT NULL COMMENT '检测方法',
  `status` int DEFAULT '0' COMMENT '样品状态：0-待采集，1-已采集，2-已送检，3-检测中，4-已完成',
  `collection_time` datetime DEFAULT NULL COMMENT '采集时间',
  `submission_time` datetime DEFAULT NULL COMMENT '送检时间',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` text COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  -- 索引和外键约束
  KEY `idx_sample_record_assignment_id` (`sampling_task_assignment_id`),
  KEY `idx_sample_record_cycle_item_id` (`detection_cycle_item_id`),
  KEY `idx_sample_record_quotation_item_id` (`project_quotation_item_id`),
  KEY `idx_sample_record_status` (`status`),
  KEY `idx_sample_record_sample_number` (`sample_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样品记录表';
```

## 部署说明

### 1. 数据库迁移

```bash
# 执行数据库迁移脚本
mysql -u username -p database_name < back/migrations/create_sample_record_table.sql
```

### 2. 后端部署

1. 确保样品记录控制器已在`server.py`中注册
2. 重启后端服务

### 3. 前端部署

1. 确保前端API接口文件已正确放置
2. 重新构建前端项目

## 使用流程

### 1. 开始执行任务

1. 在采样执行页面点击"开始执行"按钮
2. 系统自动更新任务状态为"执行中"
3. 系统自动生成样品记录

### 2. 样品管理

1. 在采样执行页面点击"样品管理"按钮
2. 查看该任务的所有样品记录
3. 支持单个或批量操作样品状态
4. 查看样品统计信息

### 3. 样品状态流转

- **待采集** → **已采集**：现场采集样品后更新状态
- **已采集** → **已送检**：样品送到实验室后更新状态
- **已送检** → **检测中**：实验室开始检测后更新状态
- **检测中** → **已完成**：检测完成后更新状态

## 测试

### 单元测试

```bash
# 运行样品记录功能测试
pytest back/tests/test_sample_record.py -v
```

### 功能测试

1. 创建项目报价并审核通过
2. 生成检测周期条目
3. 创建采样任务并分配执行人
4. 开始执行任务，验证样品记录自动生成
5. 测试样品管理功能和状态流转

## 注意事项

1. **数据一致性**：样品记录与执行任务、周期条目、项目报价明细保持关联
2. **性能优化**：大量样品记录时考虑分页查询和批量操作
3. **权限控制**：确保只有相关人员可以操作样品记录
4. **状态管理**：严格控制样品状态流转，防止非法状态变更
5. **现场直读过滤**：确保技术手册中的分析类型字段正确配置
