# 项目报价后端优化总结

## 优化背景
基于前端报价页面的改造，后端需要适配新的入参字段结构，主要变更包括：
1. 项目报价明细字段结构调整
2. 删除价格相关字段（后续通过技术手册和价格表计算）
3. 增加新的检测和采样信息字段

## 主要变更内容

### 1. 数据库表结构优化

#### project_quotation_item 表字段变更

**新增字段：**
- `classification` VARCHAR(50) - 分类
- `limitation_scope` VARCHAR(200) - 限制范围  
- `sample_source` VARCHAR(50) - 样品来源
- `is_subcontract` VARCHAR(1) - 是否分包(0-否,1-是)

**修改字段：**
- `parameter` VARCHAR(100) - 检测参数(指标)，长度从50增加到100
- `method` VARCHAR(200) - 检测方法，长度从100增加到200
- `category` VARCHAR(50) - 检测类别(二级分类)，注释更新
- `cycle_type` VARCHAR(10) - 检测周期类型，注释更新
- `cycle_count` INT - 检测周期数，注释更新
- `frequency` INT - 检测频次数，注释更新

**删除字段：**
- `service_type` - 服务类型（移至主表）
- `test_code` - 检测编号（后续通过技术手册获取）
- `price_code` - 报价编号（后续通过价格表获取）
- `sampling_price` - 采样单价（后续计算）
- `testing_price` - 检测单价（后续计算）
- `travel_price` - 差旅费单价（后续计算）
- `total_price` - 总价（后续计算）

### 2. VO模型调整

#### ProjectQuotationItemModel 字段更新
```python
# 检测信息
classification: Optional[str] = Field(default=None, description="分类")
category: str = Field(..., description="检测类别(二级分类)")
parameter: str = Field(..., description="检测参数(指标)", max_length=100)
method: str = Field(..., description="检测方法", max_length=200)
limitation_scope: Optional[str] = Field(default=None, description="限制范围")

# 采样信息
sample_source: Optional[str] = Field(default=None, description="样品来源")
point_name: Optional[str] = Field(default=None, description="点位名称")
point_count: int = Field(default=1, description="点位数")
cycle_type: Optional[str] = Field(default=None, description="检测周期类型")
cycle_count: Optional[int] = Field(default=1, description="检测周期数")
frequency: Optional[int] = Field(default=1, description="检测频次数")
sample_count: Optional[int] = Field(default=1, description="样品数")
is_subcontract: Optional[str] = Field(default="0", description="是否分包")

# 备注
remark: Optional[str] = Field(default=None, description="备注")
```

#### AddProjectQuotationModel 调整
- `service_type` 字段改为可选，不再强制要求

### 3. 服务层代码优化

#### 价格计算逻辑调整
- 移除了原有的价格计算逻辑
- 为后续集成技术手册和价格表预留接口

#### 项目报价明细创建逻辑更新
```python
project_quotation_item = ProjectQuotationItem(
    project_quotation_id=project_quotation.id,
    item_code=item_code,
    classification=item.classification,
    category=item.category,
    parameter=item.parameter,
    method=item.method,
    limitation_scope=item.limitation_scope,
    sample_source=item.sample_source,
    point_name=item.point_name,
    point_count=item.point_count or 1,
    cycle_type=item.cycle_type,
    cycle_count=item.cycle_count or 1,
    frequency=item.frequency or 1,
    sample_count=item.sample_count or 1,
    is_subcontract=item.is_subcontract or "0",
    remark=item.remark,
    # ... 其他字段
)
```

### 4. 接口兼容性

#### 客户搜索接口
- 路径：`/quotation/project-quotation/customer/search`
- 参数：`keyword` (必填)
- 返回：客户列表，包含id、name、address、contactName、contactPhone

#### 用户岗位接口
- 路径：`/system/user/`
- 返回：用户列表，包含岗位信息

## 前端数据结构示例

```json
{
  "projectName": "环境监测项目",
  "contractCode": "HT2024001",
  "serviceType": "",
  "customerName": "测试客户",
  "items": [
    {
      "category": "环境空气和废气",
      "classification": "气",
      "parameter": "1,1-二氯乙烷",
      "method": "环境空气 挥发性卤代烃的测定 活性炭吸附-二硫化碳解吸/气相色谱法 HJ 645-2013",
      "limitationScope": "检测限制范围",
      "remark": "项目备注",
      "pointName": "废气排放口",
      "pointCount": 1,
      "cycleType": "日",
      "cycleCount": 1,
      "frequency": 1,
      "sampleCount": 1,
      "sampleSource": "现场采集",
      "isSubcontract": "0"
    }
  ]
}
```

## 数据库迁移

执行迁移脚本：`back/migrations/update_project_quotation_item_table.sql`

## 后续优化建议

1. **价格计算集成**：集成技术手册和价格表，实现自动价格计算
2. **数据验证增强**：添加更多业务规则验证
3. **性能优化**：对新增的字段添加适当索引
4. **日志记录**：增加详细的操作日志记录

## 测试验证

运行测试脚本：`python back/test_new_project_quotation_structure.py`

## 影响范围

- ✅ 项目报价新增功能
- ✅ 项目报价编辑功能  
- ✅ 项目报价查询功能
- ⚠️ 需要测试现有的导出功能
- ⚠️ 需要验证报价审批流程

## 完成状态

- [x] 数据库表结构调整
- [x] VO模型更新
- [x] 服务层代码优化
- [x] 接口兼容性确认
- [x] 迁移脚本创建
- [x] 测试用例编写
- [ ] 集成测试验证
- [ ] 生产环境部署
