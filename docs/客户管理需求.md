## 客户管理功能开发
### 功能目标
本模块用于对实验室业务相关客户信息进行管理，支持客户的录入、查询、修改和删除操作，确保客户资料完整、准确，便于业务流程开展。
### 功能模块划分
#### 客户信息管理
1. 支持新增、编辑、删除、查询客户信息
2. 删除操作需具备相应权限（如管理员权限）
### 客户信息结构设计
1. 客户分为两级结构：
 * 集团（一级客户）
 * 公司（二级客户，必须归属于某个集团）
2. 每个公司名称在系统中必须唯一，需做查重校验
3. 后续可能有新增层级的需求，需在开发阶段考虑后续影响
### 客户基础信息字段
1. 每个客户需维护以下信息：

| 字段名称 | 说明 | 是否必填 | 备注 |
| -------- | ---- | -------- | ---- |
| 客户类型 | 政府/企业/园区 | 是 | 单选 |
| 客户名称 | 客户的名称 | 是 | 公司名称需唯一 |
| 所属集团 | 公司时需填写 | 否 | 仅需要时填写 |
| 客户来源 | 下拉菜单选择 | 是 | 单选 |
| 地址 | 客户地址 | 是 | 地址框，允许只选到市本级 |
| 详细地址 | 客户地址 | 否 | 输入框 |
| 联系人列表 | 可添加多个联系人 | 是 | 至少添加一个 |
| 联系方式 | 包括电话、微信等 | 否 | 每个联系人可设联系方式 |
| 内部负责人列表 | 可添加多个负责人 | 是 | 至少添加一个 |
| 邮箱地址 | 客户邮箱 | 否 | 多联系人可配置多邮箱，支持设置企业公用邮箱 |
### 操作权限要求

| 操作类型 | 权限要求 | 备注 |
| -------- | -------- | ---- |
| 新增 | 普通用户可用 | |
| 编辑 | 客户负责人可用 | |
| 查询 | 所有人可用 | 需按条件查询支持分页，筛选条件包括集团、客户名称、来源、地区、负责人 |
| 删除 | 管理员权限 | 逻辑删除建议保留历史数据 |
### 功能设计要点
1. **联系人管理**：支持每个客户下维护多个联系人，联系人可单独设置姓名、职务、联系方式、邮箱等
2. **负责人管理**：内部负责人为本公司员工，可选多名，用于后续业务流转
3. **客户查重**：新增公司级客户时，系统自动校验是否存在重名公司
4. **分级显示**：前端支持集团-公司两级展示，利于筛选与结构管理
5. **搜索功能**：支持根据客户名称、联系人、负责人、地址模糊搜索