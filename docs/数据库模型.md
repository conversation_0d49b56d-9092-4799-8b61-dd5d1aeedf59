# LIMS实验室管理系统数据库模型

## 数据库模型概述

本数据库模型基于LIMS实验室管理系统的需求分析，主要包含以下几个核心模块：

1. **客户管理模块**：包括客户表、联系人表和内部负责人表，支持集团-公司两级客户结构管理。
2. **报价管理模块**：包括报价单表、报价项目表、报价附件表和操作日志表，支持报价流程的全生命周期管理。
3. **基础数据模块**：包括技术手册表和价目表，作为系统可配置的数据源。
4. **用户管理模块**：包括用户表，支持系统用户管理和权限控制。

各表之间的主要关联关系如下：
- 客户表与联系人表、内部负责人表是一对多关系
- 报价单表与客户表是多对一关系
- 报价单表与报价项目表、报价附件表、操作日志表是一对多关系
- 报价项目表与技术手册表、价目表通过检测参数和方法关联

下面是各表的详细设计：

## 1. 客户表（Customers）

**描述**：存储所有客户的基本信息，支持集团-公司两级结构。

**关联关系**：
- 与自身存在自关联（parent_id），用于表示集团-公司的层级关系
- 与联系人表（Contacts）是一对多关系
- 与内部负责人表（Internal_Managers）是一对多关系
- 与报价单表（Quotations）是一对多关系

**索引设计**：
- 主键索引：id
- 唯一索引：name（确保公司名称唯一）
- 普通索引：parent_id, customer_type, status, created_at

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| customer_type | ENUM | 是 | 否 | 客户类型（政府/企业/园区） |
| name | VARCHAR(100) | 是 | 否 | 客户名称（唯一） |
| parent_id | INT | 否 | 否 | 外键，关联客户表（所属集团）（自关联） |
| source | VARCHAR(50) | 否 | 否 | 客户来源 |
| province | VARCHAR(50) | 是 | 否 | 省份 |
| city | VARCHAR(50) | 是 | 否 | 城市 |
| address | TEXT | 否 | 否 | 详细地址 |
| status | ENUM | 是 | 否 | 状态（正常/禁用/删除） |
| created_by | INT | 是 | 否 | 外键，关联用户表(Users)（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表(Users)（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |

## 2. 联系人表（Contacts）

**描述**：存储客户的联系人信息，每个客户可以有多个联系人。

**关联关系**：
- 与客户表（Customers）是多对一关系
- 与用户表（Users）是一对一关系
- 与报价单表（Quotations）是一对多关系

**索引设计**：
- 主键索引：id
- 普通索引：customer_id, user_id, is_primary

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| customer_id | INT | 是 | 否 | 外键，关联客户表 |
| user_id | INT | 是 | 否 | 外键，关联用户表 |
| position | VARCHAR(50) | 否 | 否 | 职务 |
| is_primary | BOOLEAN | 是 | 否 | 是否为主要联系人 |
| created_by | INT | 是 | 否 | 外键，关联用户表（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |

## 3. 内部负责人表（Internal_Managers）

**描述**：存储客户的内部负责人信息，每个客户可以有多个内部负责人。

**关联关系**：
- 与客户表（Customers）是多对一关系
- 与用户表（Users）是多对一关系

**索引设计**：
- 主键索引：id
- 普通索引：customer_id, user_id

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| customer_id | INT | 是 | 否 | 外键，关联客户表 |
| user_id | INT | 是 | 否 | 外键，关联用户表 |
| created_by | INT | 是 | 否 | 外键，关联用户表（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |

## 4. 报价单表（Quotations）

**描述**：存储报价单的主要信息，包括客户信息、受检方信息、价格信息和审批状态。

**关联关系**：
- 与客户表（Customers）是多对一关系
- 与联系人表（Contacts）是多对一关系
- 与报价项目表（Quotation_Items）是一对多关系
- 与报价附件表（Quotation_Attachments）是一对多关系
- 与操作日志表（Operation_Logs）是一对多关系

**索引设计**：
- 主键索引：id
- 唯一索引：quotation_number
- 普通索引：customer_id, contact_id, status, created_at, created_by

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| quotation_number | VARCHAR(50) | 是 | 否 | 报价单编号（唯一，自动生成） |
| customer_id | INT | 是 | 否 | 外键，关联客户表(Customers) |
| inspected_company | VARCHAR(100) | 是 | 否 | 受检方企业名称 |
| contact_id | INT | 是 | 否 | 外键，关联联系人表(Contacts) |
| contact_address | TEXT | 是 | 否 | 受检方详细地址 |
| total_amount | DECIMAL(10,2) | 是 | 否 | 检测项目总金额 |
| discount_rate | DECIMAL(5,2) | 否 | 否 | 折扣率 |
| discounted_amount | DECIMAL(10,2) | 是 | 否 | 折后检测费 |
| other_fees | DECIMAL(10,2) | 否 | 否 | 其他费用（差旅费、报告费等） |
| tax_rate | DECIMAL(5,2) | 是 | 否 | 税率 |
| tax_amount | DECIMAL(10,2) | 是 | 否 | 税费 |
| adjustment_amount | DECIMAL(10,2) | 否 | 否 | 整体调整金额（正负皆可） |
| final_amount | DECIMAL(10,2) | 是 | 否 | 最终报价总价 |
| status | ENUM | 是 | 否 | 状态（草稿/待审核/已审核/已退回） |
| created_by | INT | 是 | 否 | 外键，关联用户表(Users)（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表(Users)（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |
| user_approved_by | INT | 否 | 否 | 外键，关联用户表(Users)（审核人） |
| approved_at | TIMESTAMP | 否 | 否 | 审核时间 |

## 5. 报价项目表（Quotation_Items）

**描述**：存储报价单中的具体检测项目信息，包括检测参数、方法、点位、周期等。

**关联关系**：
- 与报价单表（Quotations）是多对一关系
- 与技术手册表（Technical_Manuals）是多对一关系（通过检测类别、参数和方法）
- 与价目表（Price_List）是多对一关系（通过检测参数和方法）

**索引设计**：
- 主键索引：id
- 普通索引：quotation_id, test_category, test_parameter, test_method

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| quotation_id | INT | 是 | 否 | 外键，关联报价单表(Quotations) |
| tech_manual_category | VARCHAR(50) | 是 | 否 | 检测类别，关联技术手册表(Technical_Manuals) |
| tech_manual_parameter | VARCHAR(50) | 是 | 否 | 检测参数，关联技术手册表(Technical_Manuals) |
| tech_manual_method | VARCHAR(100) | 是 | 否 | 检测方法，关联技术手册表(Technical_Manuals) |
| point_name | VARCHAR(100) | 是 | 否 | 点位名称 |
| point_count | INT | 是 | 否 | 点位数 |
| cycle_type | ENUM | 是 | 否 | 周期类型（日/周/月/季/年） |
| cycle_count | INT | 是 | 否 | 周期数 |
| frequency | INT | 是 | 否 | 频次数 |
| sample_count | INT | 是 | 否 | 样品数 |
| service_type | ENUM | 是 | 否 | 服务类型（采样检测/送样检测） |
| sampling_fee | DECIMAL(10,2) | 否 | 否 | 采样费 |
| testing_fee | DECIMAL(10,2) | 是 | 否 | 检测费 |
| travel_expense | DECIMAL(10,2) | 否 | 否 | 差旅费 |
| total_fee | DECIMAL(10,2) | 是 | 否 | 总费用 |
| created_by | INT | 是 | 否 | 外键，关联用户表(Users)（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表(Users)（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |

## 6. 报价附件表（Quotation_Attachments）

**描述**：存储报价单相关的附件信息，如采样方案、报价单导出文件等。

**关联关系**：
- 与报价单表（Quotations）是多对一关系

**索引设计**：
- 主键索引：id
- 普通索引：quotation_id, file_type

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| quotation_id | INT | 是 | 否 | 外键，关联报价单表(Quotations) |
| file_name | VARCHAR(255) | 是 | 否 | 文件名 |
| file_path | VARCHAR(255) | 是 | 否 | 文件路径 |
| file_type | VARCHAR(50) | 是 | 否 | 文件类型（PDF/Word/Excel/采样方案） |
| created_by | INT | 是 | 否 | 外键，关联用户表(Users)（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表(Users)（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |

## 7. 操作日志表（Operation_Logs）

**描述**：记录报价单相关的所有操作日志，包括创建、修改、提交、审批、退回等操作。

**关联关系**：
- 与报价单表（Quotations）是多对一关系
- 与用户表（Users）是多对一关系

**索引设计**：
- 主键索引：id
- 普通索引：quotation_id, user_id, operation_time, operation_type

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| quotation_id | INT | 是 | 否 | 外键，关联报价单表(Quotations) |
| user_id | INT | 是 | 否 | 外键，关联用户表(Users) |
| operation_type | ENUM | 是 | 否 | 操作类型（创建/修改/提交/审批/退回） |
| operation_time | TIMESTAMP | 是 | 否 | 操作时间 |
| operation_details | TEXT | 是 | 否 | 操作详情 |

## 8. 技术手册表（Technical_Manuals）

**描述**：存储检测参数和方法的技术信息，作为系统可配置的数据源。

**关联关系**：
- 与报价项目表（Quotation_Items）是一对多关系（通过检测类别、参数和方法）

**索引设计**：
- 主键索引：id
- 普通索引：category, parameter, method
- 唯一索引：category_parameter_method（组合唯一索引）

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| category | VARCHAR(50) | 是 | 否 | 检测类别 |
| parameter | VARCHAR(50) | 是 | 否 | 检测参数 |
| method | VARCHAR(100) | 是 | 否 | 检测方法 |
| description | TEXT | 否 | 否 | 技术描述 |
| created_by | INT | 是 | 否 | 外键，关联用户表(Users)（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表(Users)（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |

## 9. 价目表（Price_List）

**描述**：存储检测参数和方法的价格信息，作为系统可配置的数据源。

**关联关系**：
- 与报价项目表（Quotation_Items）是一对多关系（通过检测参数和方法）

**索引设计**：
- 主键索引：id
- 普通索引：parameter, method
- 唯一索引：parameter_method（组合唯一索引）

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| parameter | VARCHAR(50) | 是 | 否 | 检测参数 |
| method | VARCHAR(100) | 是 | 否 | 检测方法 |
| sampling_price | DECIMAL(10,2) | 否 | 否 | 采样单价 |
| testing_price | DECIMAL(10,2) | 是 | 否 | 检测单价 |
| travel_price | DECIMAL(10,2) | 否 | 否 | 差旅费单价 |
| created_by | INT | 是 | 否 | 外键，关联用户表（创建人） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_by | INT | 是 | 否 | 外键，关联用户表（更新人） |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |

## 10. 用户表（Users）

**描述**：存储所有用户信息，包括客户联系人和内部用户。

**关联关系**：
- 与联系人表（Contacts）是一对一关系
- 与内部负责人表（Internal_Managers）是一对多关系
- 与报价单表（Quotations）是一对多关系（创建人、更新人、审核人）
- 与操作日志表（Operation_Logs）是一对多关系
- 与各表的created_by和updated_by字段关联

**索引设计**：
- 主键索引：id
- 唯一索引：username, email
- 普通索引：role, status

| 字段名 | 类型 | 必填 | 主键 | 说明 |
|--------|------|------|------|------|
| id | INT | 是 | 是 | 自增主键 |
| username | VARCHAR(50) | 是 | 否 | 用户名（唯一） |
| password | VARCHAR(255) | 是 | 否 | 加密后的密码 |
| name | VARCHAR(50) | 是 | 否 | 姓名 |
| email | VARCHAR(100) | 是 | 否 | 电子邮箱（唯一） |
| phone | VARCHAR(20) | 否 | 否 | 电话 |
| wechat | VARCHAR(50) | 否 | 否 | 微信 |
| role | ENUM | 是 | 否 | 用户角色（管理员/业务员/市场负责人等） |
| status | ENUM | 是 | 否 | 状态（正常/禁用） |
| created_at | TIMESTAMP | 是 | 否 | 创建时间 |
| updated_at | TIMESTAMP | 是 | 否 | 更新时间 |