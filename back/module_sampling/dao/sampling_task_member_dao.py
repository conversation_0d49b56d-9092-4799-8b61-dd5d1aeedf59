"""
采样任务组员数据访问层
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from module_sampling.entity.do.sampling_task_member_do import SamplingTaskMember


class SamplingTaskMemberDAO:
    """采样任务组员DAO"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_members_by_task_id(self, task_id: int) -> List[SamplingTaskMember]:
        """根据任务ID获取组员列表"""
        query = select(SamplingTaskMember).where(
            SamplingTaskMember.sampling_task_id == task_id
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_member_by_task_and_user(self, task_id: int, user_id: int) -> Optional[SamplingTaskMember]:
        """根据任务ID和用户ID获取组员记录"""
        query = select(SamplingTaskMember).where(
            SamplingTaskMember.sampling_task_id == task_id,
            SamplingTaskMember.user_id == user_id
        )
        result = await self.db.execute(query)
        return result.scalars().first()

    async def create_member(self, member: SamplingTaskMember) -> SamplingTaskMember:
        """创建组员记录"""
        self.db.add(member)
        await self.db.flush()
        await self.db.refresh(member)
        return member

    async def batch_create_members(self, members: List[SamplingTaskMember]) -> List[SamplingTaskMember]:
        """批量创建组员记录"""
        self.db.add_all(members)
        await self.db.flush()
        for member in members:
            await self.db.refresh(member)
        return members

    async def delete_members_by_task_id(self, task_id: int) -> int:
        """删除任务的所有组员"""
        query = delete(SamplingTaskMember).where(
            SamplingTaskMember.sampling_task_id == task_id
        )
        result = await self.db.execute(query)
        return result.rowcount

    async def delete_member_by_task_and_user(self, task_id: int, user_id: int) -> int:
        """删除指定任务的指定用户组员记录"""
        query = delete(SamplingTaskMember).where(
            SamplingTaskMember.sampling_task_id == task_id,
            SamplingTaskMember.user_id == user_id
        )
        result = await self.db.execute(query)
        return result.rowcount
