"""
瓶组编号序列表数据模型
"""

from sqlalchemy import Column, Integer, BigInteger, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from config.database import Base


class SamplingBottleGroupSequence(Base):
    """
    瓶组编号序列表
    """
    __tablename__ = 'sampling_bottle_group_sequence'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    
    # 业务字段
    current_sequence = Column(Integer, default=0, comment='当前序号')
    
    # 审计字段
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 约束
    __table_args__ = (
        UniqueConstraint('sampling_task_id', name='uk_task_sequence'),
        {'comment': '瓶组编号序列表'}
    )
    
    def __repr__(self):
        return f"<SamplingBottleGroupSequence(id={self.id}, sampling_task_id={self.sampling_task_id}, current_sequence={self.current_sequence})>"
