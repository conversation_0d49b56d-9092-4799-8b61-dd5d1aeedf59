"""
瓶组样品关联表数据模型
"""

from sqlalchemy import Column, BigInteger, DateTime, ForeignKey, UniqueConstraint, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SamplingBottleGroupSample(Base):
    """
    瓶组样品关联表
    """
    __tablename__ = 'sampling_bottle_group_sample'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    bottle_group_id = Column(BigInteger, ForeignKey('sampling_bottle_group.id'), nullable=False, comment='瓶组ID')
    sample_record_id = Column(BigInteger, ForeignKey('sample_record.id'), nullable=False, comment='样品记录ID')
    
    # 审计字段
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    
    # 关系定义（暂时注释，避免循环导入）
    # bottle_group = relationship("SamplingBottleGroup", back_populates="sample_relations")
    # sample_record = relationship("SampleRecord", back_populates="bottle_group_relations")
    
    # 索引和约束
    __table_args__ = (
        UniqueConstraint('bottle_group_id', 'sample_record_id', name='uk_bottle_sample'),
        Index('idx_sample_record_id', 'sample_record_id'),
        {'comment': '瓶组样品关联表'}
    )
    
    def __repr__(self):
        return f"<SamplingBottleGroupSample(id={self.id}, bottle_group_id={self.bottle_group_id}, sample_record_id={self.sample_record_id})>"
