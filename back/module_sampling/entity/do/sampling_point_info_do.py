"""
采样点位信息数据模型
"""

from sqlalchemy import Column, BigInteger, String, DateTime, Text, ForeignKey, Index, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SamplingPointInfo(Base):
    """采样点位信息表"""
    __tablename__ = 'sampling_point_info'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_group_id = Column(BigInteger, ForeignKey('sampling_task_group.id'), nullable=False, comment='采样任务分组ID')
    point_name = Column(String(200), comment='点位名称')
    
    # 点位照片信息
    point_photo_url = Column(String(500), comment='采样点位照片URL')
    point_photo_name = Column(String(200), comment='采样点位照片文件名')
    
    # 环境照片信息（东南西北4张）
    east_photo_url = Column(String(500), comment='东侧环境照片URL')
    east_photo_name = Column(String(200), comment='东侧环境照片文件名')
    south_photo_url = Column(String(500), comment='南侧环境照片URL')
    south_photo_name = Column(String(200), comment='南侧环境照片文件名')
    west_photo_url = Column(String(500), comment='西侧环境照片URL')
    west_photo_name = Column(String(200), comment='西侧环境照片文件名')
    north_photo_url = Column(String(500), comment='北侧环境照片URL')
    north_photo_name = Column(String(200), comment='北侧环境照片文件名')
    
    # 经纬度信息
    longitude = Column(DECIMAL(10, 7), comment='经度')
    latitude = Column(DECIMAL(10, 7), comment='纬度')
    coordinate_system = Column(String(50), default='WGS84', comment='坐标系统')
    
    # 其他信息
    altitude = Column(DECIMAL(8, 2), comment='海拔高度（米）')
    sampling_time = Column(DateTime, comment='采样时间')
    weather_condition = Column(String(100), comment='天气状况')
    temperature = Column(DECIMAL(5, 2), comment='温度（摄氏度）')
    humidity = Column(DECIMAL(5, 2), comment='湿度（%）')
    wind_speed = Column(DECIMAL(5, 2), comment='风速（m/s）')
    wind_direction = Column(String(20), comment='风向')
    remarks = Column(Text, comment='备注信息')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义（暂时注释，避免循环导入）
    # sampling_task_group = relationship("SamplingTaskGroup", foreign_keys=[sampling_task_group_id])
    # creator = relationship("SysUser", foreign_keys=[create_by])
    # updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引
    __table_args__ = (
        Index('idx_sampling_point_info_group_id', 'sampling_task_group_id'),
        Index('idx_sampling_point_info_point_name', 'point_name'),
        Index('idx_sampling_point_info_create_time', 'create_time'),
        {'comment': '采样点位信息表'}
    )
    
    def __repr__(self):
        return f"<SamplingPointInfo(id={self.id}, sampling_task_group_id={self.sampling_task_group_id}, point_name='{self.point_name}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sampling_task_group_id': self.sampling_task_group_id,
            'point_name': self.point_name,
            'point_photo_url': self.point_photo_url,
            'point_photo_name': self.point_photo_name,
            'east_photo_url': self.east_photo_url,
            'east_photo_name': self.east_photo_name,
            'south_photo_url': self.south_photo_url,
            'south_photo_name': self.south_photo_name,
            'west_photo_url': self.west_photo_url,
            'west_photo_name': self.west_photo_name,
            'north_photo_url': self.north_photo_url,
            'north_photo_name': self.north_photo_name,
            'longitude': float(self.longitude) if self.longitude else None,
            'latitude': float(self.latitude) if self.latitude else None,
            'coordinate_system': self.coordinate_system,
            'altitude': float(self.altitude) if self.altitude else None,
            'sampling_time': self.sampling_time.isoformat() if self.sampling_time else None,
            'weather_condition': self.weather_condition,
            'temperature': float(self.temperature) if self.temperature else None,
            'humidity': float(self.humidity) if self.humidity else None,
            'wind_speed': float(self.wind_speed) if self.wind_speed else None,
            'wind_direction': self.wind_direction,
            'remarks': self.remarks,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
