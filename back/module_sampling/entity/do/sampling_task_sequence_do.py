from sqlalchemy import Column, BigInteger, String, Integer, DateTime, UniqueConstraint
from sqlalchemy.sql import func
from config.database import Base


class SamplingTaskSequence(Base):
    """采样任务编号序列表"""
    __tablename__ = 'sampling_task_sequence'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False, comment='主键ID')
    
    # 业务字段
    year_month = Column(String(4), nullable=False, comment='年月（YYMM）')
    sequence_number = Column(Integer, default=0, comment='当月序号')
    
    # 审计字段
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 约束
    __table_args__ = (
        UniqueConstraint('year_month', name='uk_year_month'),
        {'comment': '采样任务编号序列表'}
    )
    
    def __repr__(self):
        return f"<SamplingTaskSequence(id={self.id}, year_month='{self.year_month}', sequence_number={self.sequence_number})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'year_month': self.year_month,
            'sequence_number': self.sequence_number,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }