from sqlalchemy import Column, BigInteger, DateTime, ForeignKey, Index, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SamplingTaskCycleItem(Base):
    """采样任务与检测周期关联表"""
    __tablename__ = 'sampling_task_cycle_item'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    detection_cycle_item_id = Column(BigInteger, ForeignKey('detection_cycle_item.id'), nullable=False, comment='检测周期条目ID')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    
    # 关系定义
    sampling_task = relationship("SamplingTask", back_populates="cycle_item_relations")
    detection_cycle_item = relationship("DetectionCycleItem", back_populates="sampling_task_relations")
    
    # 创建人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    
    # 索引和约束
    __table_args__ = (
        UniqueConstraint('sampling_task_id', 'detection_cycle_item_id', name='uk_task_cycle'),
        Index('idx_sampling_task_id', 'sampling_task_id'),
        Index('idx_detection_cycle_item_id', 'detection_cycle_item_id'),
        {'comment': '采样任务与检测周期关联表'}
    )
    
    def __repr__(self):
        return f"<SamplingTaskCycleItem(id={self.id}, sampling_task_id={self.sampling_task_id}, detection_cycle_item_id={self.detection_cycle_item_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sampling_task_id': self.sampling_task_id,
            'detection_cycle_item_id': self.detection_cycle_item_id,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None
        }