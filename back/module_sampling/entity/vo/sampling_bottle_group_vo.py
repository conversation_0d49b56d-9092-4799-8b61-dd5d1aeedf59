"""
采样瓶组视图对象
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class SamplingBottleGroupModel(BaseModel):
    """采样瓶组基础模型"""
    
    id: Optional[int] = Field(None, description="主键ID")
    sampling_task_id: int = Field(..., description="采样任务ID")
    bottle_group_code: str = Field(..., description="瓶组编号")
    bottle_maintenance_id: Optional[int] = Field(None, description="瓶组管理ID，NULL表示默认瓶组")
    detection_method: Optional[str] = Field(None, description="检测方法")
    sample_count: int = Field(0, description="关联样品数量")
    status: int = Field(0, description="状态：0-待采集，1-已采集，2-已送检")
    
    class Config:
        from_attributes = True


class SamplingBottleGroupDetailModel(SamplingBottleGroupModel):
    """采样瓶组详情模型"""
    
    # 瓶组信息
    bottle_type: Optional[str] = Field(None, description="容器类型")
    bottle_volume: Optional[str] = Field(None, description="容器容量")
    storage_styles: Optional[List[str]] = Field(None, description="存储方式")
    fix_styles: Optional[List[str]] = Field(None, description="固定方式")
    sample_age: Optional[int] = Field(None, description="样品时效数值")
    sample_age_unit: Optional[str] = Field(None, description="样品时效单位")
    
    # 任务信息
    task_code: Optional[str] = Field(None, description="任务编号")
    task_name: Optional[str] = Field(None, description="任务名称")
    
    # 关联样品信息
    samples: Optional[List[dict]] = Field(None, description="关联的样品列表")
    
    # 审计信息
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_time: Optional[datetime] = Field(None, description="更新时间")


class SamplingBottleGroupCreateModel(BaseModel):
    """创建采样瓶组模型"""
    
    sampling_task_id: int = Field(..., description="采样任务ID")
    bottle_maintenance_id: Optional[int] = Field(None, description="瓶组管理ID，NULL表示默认瓶组")
    detection_method: Optional[str] = Field(None, description="检测方法")
    sample_record_ids: List[int] = Field(..., description="关联的样品记录ID列表")


class SamplingBottleGroupUpdateModel(BaseModel):
    """更新采样瓶组模型"""
    
    status: Optional[int] = Field(None, description="状态：0-待采集，1-已采集，2-已送检")


class SamplingBottleGroupQueryModel(BaseModel):
    """查询采样瓶组模型"""
    
    sampling_task_id: Optional[int] = Field(None, description="采样任务ID")
    bottle_maintenance_id: Optional[int] = Field(None, description="瓶组管理ID")
    detection_method: Optional[str] = Field(None, description="检测方法")
    status: Optional[int] = Field(None, description="状态")



class BottleGroupGenerateResponseModel(BaseModel):
    """瓶组生成响应模型"""
    
    total_groups: int = Field(..., description="生成的瓶组总数")
    default_groups: int = Field(..., description="默认瓶组数量")
    matched_groups: int = Field(..., description="匹配到瓶组的数量")
    bottle_groups: List[SamplingBottleGroupDetailModel] = Field(..., description="生成的瓶组列表")


class SampleRecordWithDetectionModel(BaseModel):
    """样品记录及检测信息模型"""
    
    sample_id: int = Field(..., description="样品记录ID")
    sample_number: int = Field(..., description="样品序号")
    sample_type: Optional[str] = Field(None, description="样品类型")
    sample_source: Optional[str] = Field(None, description="样品来源")
    point_name: Optional[str] = Field(None, description="点位名称")
    detection_method: str = Field(..., description="检测方法")
    detection_category: Optional[str] = Field(None, description="检测类别")
    detection_parameter: Optional[str] = Field(None, description="检测参数")
