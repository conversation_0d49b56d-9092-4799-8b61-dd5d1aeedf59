import json
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from module_sampling.dao.sampling_task_group_dao import SamplingTaskGroupDAO
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_admin.entity.do.user_do import SysUser
from module_sampling.dto.sampling_task_group_dto import (
    SamplingTaskGroupDTO, 
    SamplingTaskGroupCreateDTO,
    SamplingTaskGroupUpdateDTO,
    TaskGroupAssignmentRequestDTO
)
from exceptions.exception import ServiceException


class SamplingTaskGroupService:
    """采样任务分组服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.group_dao = SamplingTaskGroupDAO(db)
    
    async def create_groups_for_task(self, task_id: int, cycle_item_ids: List[int], create_by: int) -> List[SamplingTaskGroupDTO]:
        """为采样任务创建分组记录"""
        try:
            # 查询周期条目及其关联的项目报价明细信息
            stmt = select(DetectionCycleItem, ProjectQuotationItem).join(
                ProjectQuotationItem, DetectionCycleItem.project_quotation_item_id == ProjectQuotationItem.id
            ).where(DetectionCycleItem.id.in_(cycle_item_ids))
            
            result = await self.db.execute(stmt)
            cycle_items_with_quotation = result.fetchall()
            
            if not cycle_items_with_quotation:
                raise ServiceException(message="未找到有效的周期条目")
            
            # 按分组键聚合周期条目
            groups_data = {}
            for cycle_item, quotation_item in cycle_items_with_quotation:
                # 构建分组键
                group_key = (
                    cycle_item.cycle_number,
                    quotation_item.cycle_type or '',
                    quotation_item.category or '',
                    quotation_item.point_name or ''
                )
                
                if group_key not in groups_data:
                    groups_data[group_key] = {
                        'cycle_number': cycle_item.cycle_number,
                        'cycle_type': quotation_item.cycle_type,
                        'detection_category': quotation_item.category,
                        'point_name': quotation_item.point_name,
                        'cycle_item_ids': []
                    }
                
                groups_data[group_key]['cycle_item_ids'].append(cycle_item.id)
            
            # 获取采样任务信息以生成分组编号
            from module_sampling.entity.do.sampling_task_do import SamplingTask
            task_stmt = select(SamplingTask).where(SamplingTask.id == task_id)
            task_result = await self.db.execute(task_stmt)
            task = task_result.scalar_one_or_none()

            if not task:
                raise ServiceException(message="采样任务不存在")

            # 创建分组记录，直接使用分组序号生成分组编号
            groups_to_create = []
            group_index = 1  # 分组序号从1开始
            for group_data in groups_data.values():
                # 直接生成分组编号：任务编号-分组序号
                group_code = f"{task.task_code}-{group_index}"

                group = SamplingTaskGroup(
                    sampling_task_id=task_id,
                    group_code=group_code,  # 直接设置正确的分组编号
                    cycle_number=group_data['cycle_number'],
                    cycle_type=group_data['cycle_type'],
                    detection_category=group_data['detection_category'],
                    point_name=group_data['point_name'],
                    cycle_item_ids=json.dumps(group_data['cycle_item_ids']),
                    assigned_user_ids=None,  # 初始时未指派执行人
                    status=0,  # 初始状态为待执行
                    create_by=create_by,
                    update_by=create_by
                )
                groups_to_create.append(group)
                group_index += 1  # 递增分组序号

            # 批量创建分组记录
            created_groups = await self.group_dao.batch_create_groups(groups_to_create)

            # 转换为DTO并返回
            result_dtos = []
            for group in created_groups:
                dto = await self._convert_to_dto(group)
                result_dtos.append(dto)

            return result_dtos
            
        except Exception as e:
            from utils.log_util import logger
            logger.error(f"创建分组记录时发生异常: {str(e)}")
            logger.exception(e)
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"创建分组记录失败: {str(e)}")
    
    async def assign_executors_to_groups(self, request: TaskGroupAssignmentRequestDTO, current_user_id: int) -> List[SamplingTaskGroupDTO]:
        """为分组指派执行人"""
        try:
            result_groups = []
            
            for group_update in request.groups:
                # 查找对应的分组记录
                group = await self.group_dao.get_group_by_id(group_update.id)
                if not group:
                    raise ServiceException(message=f"分组记录 {group_update.id} 不存在")
                
                # 更新执行人指派
                if group_update.assigned_user_ids is not None:
                    group.assigned_user_ids = json.dumps(group_update.assigned_user_ids)

                # 更新分组状态
                if group_update.status is not None:
                    group.status = group_update.status

                group.update_by = current_user_id
                
                updated_group = await self.group_dao.update_group(group)
                result_groups.append(await self._convert_to_dto(updated_group))
            
            await self.db.commit()
            return result_groups
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"指派执行人失败: {str(e)}")
    
    async def get_group_by_id(self, group_id: int) -> Optional[dict]:
        """根据分组ID获取分组详情（包含周期条目详情）"""
        group = await self.group_dao.get_group_by_id(group_id)
        if not group:
            return None

        # 转换为DTO
        group_dto = await self._convert_to_dto(group)
        group_dict = group_dto.dict()

        # 获取周期条目详情
        cycle_items = await self._get_cycle_items_detail(group)
        group_dict['cycle_items'] = cycle_items

        return group_dict

    async def get_groups_by_task_id(self, task_id: int) -> List[SamplingTaskGroupDTO]:
        """根据任务ID获取所有分组记录"""
        groups = await self.group_dao.get_groups_by_task_id(task_id)
        return [await self._convert_to_dto(group) for group in groups]
    
    async def get_groups_by_user_id(self, user_id: int) -> List[SamplingTaskGroupDTO]:
        """根据用户ID获取分配给该用户的分组记录"""
        groups = await self.group_dao.get_groups_by_user_id(user_id)
        return [await self._convert_to_dto(group) for group in groups]

    async def get_all_groups(self, task_name: Optional[str] = None, status: Optional[str] = None,
                           page_num: int = 1, page_size: int = 10) -> List[SamplingTaskGroupDTO]:
        """获取所有分组记录（管理员权限）"""
        groups = await self.group_dao.get_all_groups(
            task_name=task_name,
            status=status,
            page_num=page_num,
            page_size=page_size
        )
        return [await self._convert_to_dto(group) for group in groups]

    async def update_group_status(self, group_id: int, status: int, update_by: int) -> SamplingTaskGroupDTO:
        """更新分组状态"""
        try:
            group = await self.group_dao.get_group_by_id(group_id)
            if not group:
                raise ServiceException(message="分组记录不存在")

            group.status = status
            group.update_by = update_by

            updated_group = await self.group_dao.update_group(group)
            await self.db.commit()

            return await self._convert_to_dto(updated_group)

        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"更新分组状态失败: {str(e)}")
    
    async def _convert_to_dto(self, group: SamplingTaskGroup) -> SamplingTaskGroupDTO:
        """转换为DTO"""
        # 解析执行人ID列表
        assigned_user_ids = []
        if group.assigned_user_ids:
            try:
                assigned_user_ids = json.loads(group.assigned_user_ids)
            except:
                assigned_user_ids = []

        # 解析周期条目ID列表
        cycle_item_ids = []
        if group.cycle_item_ids:
            try:
                cycle_item_ids = json.loads(group.cycle_item_ids)
            except:
                cycle_item_ids = []

        # 获取用户名称
        assigned_user_names = []
        if assigned_user_ids:
            stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
            result = await self.db.execute(stmt)
            assigned_user_names = [row[0] for row in result.fetchall()]

        # 获取任务相关信息
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_customer.entity.do.customer_do import Customer

        task_stmt = select(
            SamplingTask.task_code,
            SamplingTask.task_name,
            SamplingTask.status,
            ProjectQuotation.project_name,
            Customer.customer_name
        ).select_from(
            SamplingTask
        ).join(
            ProjectQuotation, SamplingTask.project_quotation_id == ProjectQuotation.id
        ).outerjoin(
            Customer, ProjectQuotation.customer_id == Customer.customer_id
        ).where(SamplingTask.id == group.sampling_task_id)

        task_result = await self.db.execute(task_stmt)
        task_info = task_result.first()

        # 构建DTO，包含任务相关信息
        dto_data = {
            'id': group.id,
            'sampling_task_id': group.sampling_task_id,
            'group_code': group.group_code,
            'cycle_number': group.cycle_number,
            'cycle_type': group.cycle_type,
            'detection_category': group.detection_category,
            'point_name': group.point_name,
            'cycle_item_ids': cycle_item_ids,
            'assigned_user_ids': assigned_user_ids,
            'assigned_user_names': assigned_user_names,
            'status': group.status if group.status is not None else 0,  # 分组状态
            'create_by': group.create_by,
            'create_time': group.create_time,
            'update_by': group.update_by,
            'update_time': group.update_time
        }

        # 添加任务相关信息
        if task_info:
            dto_data.update({
                'task_code': task_info[0],
                'task_name': task_info[1],
                'task_status': task_info[2] if task_info[2] is not None else 0,  # 任务状态
                'project_name': task_info[3],
                'customer_name': task_info[4]
            })

        return SamplingTaskGroupDTO(**dto_data)

    async def _get_cycle_items_detail(self, group: SamplingTaskGroup) -> List[dict]:
        """获取分组的周期条目详情"""
        from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
        from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
        from module_basedata.entity.do.technical_manual_do import TechnicalManual

        # 解析周期条目ID列表
        cycle_item_ids = []
        if group.cycle_item_ids:
            try:
                cycle_item_ids = json.loads(group.cycle_item_ids)
            except:
                cycle_item_ids = []

        if not cycle_item_ids:
            return []

        # 查询周期条目详情
        stmt = select(
            DetectionCycleItem.id,
            DetectionCycleItem.cycle_number,
            DetectionCycleItem.status,
            ProjectQuotationItem.parameter,
            ProjectQuotationItem.category,
            ProjectQuotationItem.point_name,
            ProjectQuotationItem.sample_count,
            ProjectQuotationItem.method,
            ProjectQuotationItem.frequency,
            ProjectQuotationItem.cycle_type
        ).select_from(
            DetectionCycleItem
        ).join(
            ProjectQuotationItem, DetectionCycleItem.project_quotation_item_id == ProjectQuotationItem.id
        ).where(DetectionCycleItem.id.in_(cycle_item_ids))

        result = await self.db.execute(stmt)
        rows = result.fetchall()

        # 构建周期条目详情列表
        cycle_items = []
        for row in rows:
            cycle_items.append({
                'id': row[0],
                'cycle_number': row[1],
                'status': row[2],
                'parameter': row[3],  # 检测参数
                'category': row[4],   # 检测类别
                'point_name': row[5],
                'sample_count': row[6],
                'method': row[7],     # 检测方法
                'frequency': row[8],  # 检测频次
                'cycle_type': row[9]  # 周期类型
            })

        return cycle_items
