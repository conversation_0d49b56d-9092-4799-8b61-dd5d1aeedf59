"""
采样瓶组控制器
"""

from fastapi import APIRouter, Depends, HTTPException, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
from module_sampling.entity.vo.sampling_bottle_group_vo import (
    SamplingBottleGroupDetailModel,
    SamplingBottleGroupUpdateModel,
    BottleGroupGenerateResponseModel
)
from utils.response_util import ResponseUtil
from utils.log_util import logger
from utils.common_util import CamelCaseUtil

router = APIRouter(prefix="/sampling/bottle-groups", tags=["采样瓶组管理"])


@router.post("/generate/{task_id}", response_model=CrudResponseModel, summary="为任务生成瓶组")
async def generate_bottle_groups(
    task_id: int = Path(..., description="采样任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    为采样任务生成瓶组
    
    Args:
        task_id: 采样任务ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        瓶组生成结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 开始为任务 {task_id} 生成瓶组")
        
        service = SamplingBottleGroupService(db)
        result = await service.generate_bottle_groups_for_task(task_id, current_user.user.user_id)
        
        # 转换为驼峰命名
        camel_result = CamelCaseUtil.transform_result(result.dict())
        
        logger.info(f"任务 {task_id} 瓶组生成成功，共生成 {result.total_groups} 个瓶组")
        return ResponseUtil.success(
            data=camel_result, 
            msg=f"瓶组生成成功，共生成 {result.total_groups} 个瓶组（默认瓶组：{result.default_groups}，匹配瓶组：{result.matched_groups}）"
        )
        
    except Exception as e:
        logger.error(f"为任务 {task_id} 生成瓶组失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sample/{sample_id}", response_model=CrudResponseModel, summary="获取样品关联的瓶组列表")
async def get_bottle_groups_by_sample(
    sample_id: int = Path(..., description="样品记录ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    获取样品记录关联的瓶组列表

    Args:
        sample_id: 样品记录ID
        db: 数据库会话
        current_user: 当前用户

    Returns:
        瓶组列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 获取样品 {sample_id} 的瓶组列表")

        service = SamplingBottleGroupService(db)
        bottle_groups = await service.get_bottle_groups_by_sample(sample_id)

        # 转换为驼峰命名
        camel_result = CamelCaseUtil.transform_result([group.dict() for group in bottle_groups])

        return ResponseUtil.success(data=camel_result)

    except Exception as e:
        logger.error(f"获取样品 {sample_id} 的瓶组列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}", response_model=CrudResponseModel, summary="获取任务的瓶组列表")
async def get_bottle_groups_by_task(
    task_id: int = Path(..., description="采样任务ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    获取任务的瓶组列表
    
    Args:
        task_id: 采样任务ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        瓶组列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 获取任务 {task_id} 的瓶组列表")
        
        service = SamplingBottleGroupService(db)
        bottle_groups = await service.get_bottle_groups_by_task_id(task_id)
        
        # 转换为驼峰命名
        camel_bottle_groups = CamelCaseUtil.transform_result([group.dict() for group in bottle_groups])
        
        logger.info(f"获取到任务 {task_id} 的 {len(bottle_groups)} 个瓶组")
        return ResponseUtil.success(data=camel_bottle_groups, msg="获取瓶组列表成功")
        
    except Exception as e:
        logger.error(f"获取任务 {task_id} 的瓶组列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{bottle_group_id}", response_model=CrudResponseModel, summary="获取瓶组详情")
async def get_bottle_group_detail(
    bottle_group_id: int = Path(..., description="瓶组ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    获取瓶组详情
    
    Args:
        bottle_group_id: 瓶组ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        瓶组详情
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 获取瓶组 {bottle_group_id} 详情")
        
        service = SamplingBottleGroupService(db)
        bottle_group = await service.get_bottle_group_detail(bottle_group_id)
        
        if not bottle_group:
            raise HTTPException(status_code=404, detail="瓶组不存在")
        
        # 转换为驼峰命名
        camel_bottle_group = CamelCaseUtil.transform_result(bottle_group.dict())
        
        return ResponseUtil.success(data=camel_bottle_group, msg="获取瓶组详情成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取瓶组 {bottle_group_id} 详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{bottle_group_id}/status", response_model=CrudResponseModel, summary="更新瓶组状态")
async def update_bottle_group_status(
    bottle_group_id: int = Path(..., description="瓶组ID"),
    status: int = Body(..., description="新状态：0-待采集，1-已采集，2-已送检"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    更新瓶组状态
    
    Args:
        bottle_group_id: 瓶组ID
        status: 新状态
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        更新结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 更新瓶组 {bottle_group_id} 状态为 {status}")
        
        service = SamplingBottleGroupService(db)
        result = await service.update_bottle_group_status(bottle_group_id, status, current_user.user.user_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="瓶组不存在或更新失败")
        
        return ResponseUtil.success(msg="瓶组状态更新成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新瓶组 {bottle_group_id} 状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{bottle_group_id}", response_model=CrudResponseModel, summary="删除瓶组")
async def delete_bottle_group(
    bottle_group_id: int = Path(..., description="瓶组ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    删除瓶组
    
    Args:
        bottle_group_id: 瓶组ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        删除结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 删除瓶组 {bottle_group_id}")
        
        service = SamplingBottleGroupService(db)
        result = await service.delete_bottle_group(bottle_group_id)
        
        if not result:
            raise HTTPException(status_code=404, detail="瓶组不存在或删除失败")
        
        return ResponseUtil.success(msg="瓶组删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除瓶组 {bottle_group_id} 失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{bottle_group_id}/samples", response_model=CrudResponseModel, summary="获取瓶组关联的样品列表")
async def get_bottle_group_samples(
    bottle_group_id: int = Path(..., description="瓶组ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
) -> dict:
    """
    获取瓶组关联的样品列表
    
    Args:
        bottle_group_id: 瓶组ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        样品列表
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 获取瓶组 {bottle_group_id} 的样品列表")
        
        service = SamplingBottleGroupService(db)
        samples = await service.bottle_group_sample_dao.get_samples_by_bottle_group_id(bottle_group_id)
        
        # 转换为驼峰命名
        camel_samples = CamelCaseUtil.transform_result([
            {
                'id': sample.id,
                'sample_number': sample.sample_number,
                'sample_type': sample.sample_type,
                'sample_source': sample.sample_source,
                'point_name': sample.point_name,
                'cycle_number': sample.cycle_number,
                'cycle_type': sample.cycle_type,
                'detection_category': sample.detection_category,
                'detection_parameter': sample.detection_parameter,
                'status': sample.status
            }
            for sample in samples
        ])
        
        return ResponseUtil.success(data=camel_samples, msg="获取瓶组样品列表成功")
        
    except Exception as e:
        logger.error(f"获取瓶组 {bottle_group_id} 的样品列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
