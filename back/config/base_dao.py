from typing import Type, Generic, TypeVar, Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase

from config.async_orm_client import AsyncORMClient

# 定义泛型类型变量
T = TypeVar('T', bound=DeclarativeBase)


class BaseDao(Generic[T]):
    """
    基础数据访问对象类，提供通用的数据库操作

    使用示例:
    ```python
    class UserDao(BaseDao[User]):
        def __init__(self, db: AsyncSession):
            super().__init__(User, db)

        async def get_user_by_username(self, username: str):
            return await self.orm_client.get_one_or_none(username=username)
    ```
    """

    def __init__(self, model_class: Type[T], db: AsyncSession):
        """
        初始化基础数据访问对象类

        :param model_class: 模型类
        :param db: 数据库会话
        """
        self.model_class = model_class
        self.db = db
        self.orm_client = AsyncORMClient(model_class, db)

    async def get_by_id(self, id: Any) -> Optional[T]:
        """
        根据ID获取实体

        :param id: 实体ID
        :return: 实体对象，如果不存在则返回None
        """
        return await self.orm_client.get_by_id(id)

    async def get_one_or_none(self, **kwargs) -> Optional[T]:
        """
        根据条件获取一个实体或None

        :param kwargs: 查询条件
        :return: 实体对象，如果不存在则返回None
        """
        return await self.orm_client.get_one_or_none(**kwargs)

    async def list_all(self, **kwargs) -> List[T]:
        """
        根据条件获取所有实体

        :param kwargs: 查询条件
        :return: 实体对象列表
        """
        return await self.orm_client.list_all(**kwargs)

    async def list_all_with_condition(self, condition: Any) -> List[T]:
        """
        根据条件获取所有实体

        :param condition: 查询条件
        :return: 实体对象列表
        """
        return await self.orm_client.list_all_with_condition(condition)

    async def create(self, data: Dict[str, Any]) -> T:
        """
        创建实体

        :param data: 实体数据
        :return: 创建的实体对象
        """
        return await self.orm_client.create(data)

    async def update(self, id: Any, data: Dict[str, Any]) -> Optional[T]:
        """
        更新实体

        :param id: 实体ID
        :param data: 更新的数据
        :return: 更新后的实体对象，如果不存在则返回None
        """
        return await self.orm_client.update(id, data)

    async def update_by_condition(self, condition: Any, data: Dict[str, Any]) -> int:
        """
        根据条件更新实体

        :param condition: 更新条件
        :param data: 更新的数据
        :return: 更新的行数
        """
        return await self.orm_client.update_by_condition(condition, data)

    async def delete(self, id: Any) -> bool:
        """
        删除实体

        :param id: 实体ID
        :return: 是否删除成功
        """
        return await self.orm_client.delete(id)

    async def soft_delete(self, id: Any, update_by: str = '') -> bool:
        """
        软删除实体（将del_flag设置为'2'）

        :param id: 实体ID
        :param update_by: 更新人
        :return: 是否删除成功
        """
        return await self.orm_client.soft_delete(id, update_by)

    async def commit(self):
        """
        提交事务
        """
        await self.orm_client.commit()

    async def rollback(self):
        """
        回滚事务
        """
        await self.orm_client.rollback()
