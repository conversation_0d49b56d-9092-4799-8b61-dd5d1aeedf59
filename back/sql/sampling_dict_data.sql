-- 采样管理模块字典数据配置

-- 1. 采样任务状态字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('采样任务状态', 'sampling_task_status', '0', 'admin', NOW(), '', NULL, '采样任务状态列表');

SET @sampling_task_status_dict_id = LAST_INSERT_ID();

-- 采样任务状态字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '待处理', 'pending', 'sampling_task_status', '', 'info', 'Y', '0', 'admin', NOW(), '', NULL, '任务刚创建，等待处理'),
(2, '进行中', 'in_progress', 'sampling_task_status', '', 'primary', 'N', '0', 'admin', NOW(), '', NULL, '任务正在执行中'),
(3, '已完成', 'completed', 'sampling_task_status', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '任务已完成'),
(4, '已暂停', 'paused', 'sampling_task_status', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '任务已暂停'),
(5, '已取消', 'cancelled', 'sampling_task_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '任务已取消');

-- 2. 检测周期状态字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('检测周期状态', 'detection_cycle_status', '0', 'admin', NOW(), '', NULL, '检测周期条目状态列表');

SET @detection_cycle_status_dict_id = LAST_INSERT_ID();

-- 检测周期状态字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '未分配', 'unassigned', 'detection_cycle_status', '', 'info', 'Y', '0', 'admin', NOW(), '', NULL, '周期条目未分配给任何任务'),
(2, '已分配', 'assigned', 'detection_cycle_status', '', 'primary', 'N', '0', 'admin', NOW(), '', NULL, '周期条目已分配给采样任务'),
(3, '进行中', 'in_progress', 'detection_cycle_status', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '周期条目正在执行中'),
(4, '已完成', 'completed', 'detection_cycle_status', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '周期条目已完成'),
(5, '已取消', 'cancelled', 'detection_cycle_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '周期条目已取消');

-- 3. 采样类型字典（如果需要）
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('采样类型', 'sampling_type', '0', 'admin', NOW(), '', NULL, '采样类型列表');

SET @sampling_type_dict_id = LAST_INSERT_ID();

-- 采样类型字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '定期采样', 'regular', 'sampling_type', '', 'primary', 'Y', '0', 'admin', NOW(), '', NULL, '按照固定周期进行采样'),
(2, '临时采样', 'temporary', 'sampling_type', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '临时性采样任务'),
(3, '应急采样', 'emergency', 'sampling_type', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '应急情况下的采样'),
(4, '补充采样', 'supplementary', 'sampling_type', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '补充性采样任务');

-- 4. 采样优先级字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('采样优先级', 'sampling_priority', '0', 'admin', NOW(), '', NULL, '采样任务优先级列表');

SET @sampling_priority_dict_id = LAST_INSERT_ID();

-- 采样优先级字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(1, '低', 'low', 'sampling_priority', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '低优先级'),
(2, '中', 'medium', 'sampling_priority', '', 'primary', 'Y', '0', 'admin', NOW(), '', NULL, '中等优先级'),
(3, '高', 'high', 'sampling_priority', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '高优先级'),
(4, '紧急', 'urgent', 'sampling_priority', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '紧急优先级');