#!/usr/bin/env python3
"""
简单的API测试
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_simple_apis():
    """测试简单的API调用"""
    
    print("🧪 测试简单的API调用...")
    
    # 直接使用我们知道的ID进行测试
    sample_id = 12
    task_id = 18
    
    print(f"\n1. 测试获取样品 {sample_id} 关联的瓶组:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            bottles = data.get('data', [])
            print(f"   ✅ 成功获取 {len(bottles)} 个瓶组")
            
            if bottles:
                # 检查第一个瓶组的数据结构
                first_bottle = bottles[0]
                print(f"   第一个瓶组数据结构:")
                for key, value in first_bottle.items():
                    print(f"     {key}: {value} ({type(value).__name__})")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    print(f"\n2. 测试获取任务 {task_id} 的瓶组:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            bottles = data.get('data', [])
            print(f"   ✅ 成功获取 {len(bottles)} 个瓶组")
            
            if bottles:
                print(f"   前3个瓶组:")
                for i, bottle in enumerate(bottles[:3], 1):
                    print(f"     {i}. {bottle.get('bottleGroupCode', 'N/A')} (状态: {bottle.get('status', 'N/A')})")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    print(f"\n3. 测试获取分组 27 的样品记录:")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/sample-records/group/27",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            samples = data.get('data', [])
            print(f"   ✅ 成功获取 {len(samples)} 个样品记录")
            
            if samples:
                for i, sample in enumerate(samples, 1):
                    print(f"     {i}. 样品 {sample.get('sampleNumber', 'N/A')} (ID: {sample.get('id', 'N/A')})")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")

if __name__ == "__main__":
    test_simple_apis()
