from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from module_quotation.entity.do.project_quotation_approver_do import ProjectQuotationApprover
from module_quotation.entity.vo.project_quotation_approver_vo import (
    ProjectQuotationApproverModel,
    AddProjectQuotationApproverModel,
    ProjectQuotationApproverQueryModel
)
from module_quotation.dao.project_quotation_approver_dao import ProjectQuotationApproverDao
from module_admin.dao.user_dao import UserDao
from utils.common_util import CamelCaseUtil
from exceptions.exception import ServiceException


class ProjectQuotationApproverService:
    """
    项目报价审核人服务层
    """

    @classmethod
    async def get_project_quotation_approvers(cls, query_db: AsyncSession, project_quotation_id: int) -> List[ProjectQuotationApproverModel]:
        """
        获取项目报价审核人列表
        
        :param query_db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 审核人列表
        """
        approvers = await ProjectQuotationApproverDao.get_approvers_by_quotation_id(query_db, project_quotation_id)
        
        result = []
        for approver in approvers:
            # 获取用户信息
            user_detail = await UserDao.get_user_detail_by_id(query_db, approver.user_id)
            if user_detail and user_detail.get('user_basic_info'):
                user = user_detail['user_basic_info']
                approver_model = ProjectQuotationApproverModel(
                    id=approver.id,
                    project_quotation_id=approver.project_quotation_id,
                    user_id=approver.user_id,
                    user_name=user.user_name,
                    nick_name=user.nick_name,
                    create_time=approver.create_time
                )
                result.append(approver_model)
        
        return result

    @classmethod
    async def add_project_quotation_approvers(cls, query_db: AsyncSession, add_model: AddProjectQuotationApproverModel, current_user: str) -> bool:
        """
        添加项目报价审核人
        
        :param query_db: 数据库会话
        :param add_model: 添加审核人模型
        :param current_user: 当前用户
        :return: 是否成功
        """
        try:
            # 先删除原有的审核人
            await ProjectQuotationApproverDao.delete_approvers_by_quotation_id(query_db, add_model.project_quotation_id)
            
            # 添加新的审核人
            for user_id in add_model.user_ids:
                # 验证用户是否存在
                user_detail = await UserDao.get_user_detail_by_id(query_db, user_id)
                if not user_detail or not user_detail.get('user_basic_info'):
                    raise ServiceException(message=f"用户ID {user_id} 不存在")
                
                approver = ProjectQuotationApprover(
                    project_quotation_id=add_model.project_quotation_id,
                    user_id=user_id,
                    create_by=current_user,
                    update_by=current_user
                )
                await ProjectQuotationApproverDao.add_approver(query_db, approver)
            
            return True
        except Exception as e:
            raise ServiceException(message=f"添加审核人失败: {str(e)}")

    @classmethod
    async def delete_project_quotation_approvers(cls, query_db: AsyncSession, project_quotation_id: int) -> bool:
        """
        删除项目报价审核人
        
        :param query_db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 是否成功
        """
        try:
            await ProjectQuotationApproverDao.delete_approvers_by_quotation_id(query_db, project_quotation_id)
            return True
        except Exception as e:
            raise ServiceException(message=f"删除审核人失败: {str(e)}")

    @classmethod
    async def get_approver_ids_by_quotation_id(cls, query_db: AsyncSession, project_quotation_id: int) -> List[int]:
        """
        根据项目报价ID获取审核人ID列表
        
        :param query_db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 审核人ID列表
        """
        approvers = await ProjectQuotationApproverDao.get_approvers_by_quotation_id(query_db, project_quotation_id)
        return [approver.user_id for approver in approvers]