"""
报价管理模块数据对象
"""

# 导入所有实体类以确保SQLAlchemy关系映射正确
from .project_quotation_do import ProjectQuotation
from .project_quotation_item_do import ProjectQuotationItem
from .project_quotation_attachment_do import ProjectQuotationAttachment
from .project_quotation_other_fee_do import ProjectQuotationOtherFee
from .project_quotation_special_consumable_do import ProjectQuotationSpecialConsumableFee
from .project_quotation_total_fee_do import ProjectQuotationTotalFee
from .project_quotation_approver_do import ProjectQuotationApprover
from .project_quotation_approval_record_do import ProjectQuotationApprovalRecord

__all__ = [
    "ProjectQuotation",
    "ProjectQuotationItem",
    "ProjectQuotationAttachment",
    "ProjectQuotationOtherFee",
    "ProjectQuotationSpecialConsumableFee",
    "ProjectQuotationTotalFee",
    "ProjectQuotationApprover",
    "ProjectQuotationApprovalRecord",
]
