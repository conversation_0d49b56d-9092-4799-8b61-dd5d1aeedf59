from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, BigInteger
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationCustomerSupport(Base):
    """
    项目报价客服关联表
    """

    __tablename__ = "project_quotation_customer_support"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment="项目报价ID")
    user_id = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=False, comment="客服用户ID")
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment="更新时间")

    # 关联项目报价表
    project_quotation = relationship("ProjectQuotation", back_populates="customer_support_relations")