from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel


class ProjectQuotationCustomerSupportModel(BaseModel):
    """
    项目报价客服模型
    """
    id: Optional[int] = Field(default=None, description="主键ID")
    project_quotation_id: int = Field(..., description="项目报价ID")
    user_id: int = Field(..., description="客服用户ID")
    user_name: Optional[str] = Field(default=None, description="客服用户名")
    nick_name: Optional[str] = Field(default=None, description="客服昵称")
    create_by: Optional[str] = Field(default=None, description="创建人")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default=None, description="更新人")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")


class AddProjectQuotationCustomerSupportModel(BaseModel):
    """
    新增项目报价客服模型
    """

    project_quotation_id: int = Field(..., description="项目报价ID")
    user_ids: List[int] = Field(..., description="客服用户ID列表")
    create_by: Optional[str] = Field(default=None, description="创建人")
    update_by: Optional[str] = Field(default=None, description="更新人")


class ProjectQuotationCustomerSupportQueryModel(BaseModel):
    """
    项目报价客服查询模型
    """

    project_quotation_id: Optional[int] = Field(default=None, description="项目报价ID")
    user_id: Optional[int] = Field(default=None, description="客服用户ID")