from typing import List
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from module_quotation.entity.do.project_quotation_approver_do import ProjectQuotationApprover


class ProjectQuotationApproverDao:
    """
    项目报价审核人数据访问层
    """

    @classmethod
    async def get_approvers_by_quotation_id(cls, db: AsyncSession, project_quotation_id: int) -> List[ProjectQuotationApprover]:
        """
        根据项目报价ID获取审核人列表
        
        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 审核人列表
        """
        query = select(ProjectQuotationApprover).where(
            ProjectQuotationApprover.project_quotation_id == project_quotation_id
        ).order_by(ProjectQuotationApprover.create_time)
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def add_approver(cls, db: AsyncSession, approver: ProjectQuotationApprover) -> ProjectQuotationApprover:
        """
        添加审核人
        
        :param db: 数据库会话
        :param approver: 审核人对象
        :return: 添加后的审核人对象
        """
        db.add(approver)
        await db.flush()
        await db.refresh(approver)
        return approver

    @classmethod
    async def delete_approvers_by_quotation_id(cls, db: AsyncSession, project_quotation_id: int) -> bool:
        """
        根据项目报价ID删除所有审核人
        
        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 是否成功
        """
        query = delete(ProjectQuotationApprover).where(
            ProjectQuotationApprover.project_quotation_id == project_quotation_id
        )
        
        await db.execute(query)
        return True

    @classmethod
    async def delete_approver_by_id(cls, db: AsyncSession, approver_id: int) -> bool:
        """
        根据审核人ID删除审核人
        
        :param db: 数据库会话
        :param approver_id: 审核人ID
        :return: 是否成功
        """
        query = delete(ProjectQuotationApprover).where(
            ProjectQuotationApprover.id == approver_id
        )
        
        await db.execute(query)
        return True

    @classmethod
    async def get_approver_by_id(cls, db: AsyncSession, approver_id: int) -> ProjectQuotationApprover:
        """
        根据审核人ID获取审核人信息
        
        :param db: 数据库会话
        :param approver_id: 审核人ID
        :return: 审核人对象
        """
        query = select(ProjectQuotationApprover).where(
            ProjectQuotationApprover.id == approver_id
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()