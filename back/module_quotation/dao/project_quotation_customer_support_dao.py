from typing import List
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from module_quotation.entity.do.project_quotation_customer_support_do import ProjectQuotationCustomerSupport


class ProjectQuotationCustomerSupportDao:
    """
    项目报价客服数据访问层
    """

    @classmethod
    async def get_customer_supports_by_quotation_id(cls, db: AsyncSession, project_quotation_id: int) -> List[ProjectQuotationCustomerSupport]:
        """
        根据项目报价ID获取客服列表
        
        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 客服列表
        """
        query = select(ProjectQuotationCustomerSupport).where(
            ProjectQuotationCustomerSupport.project_quotation_id == project_quotation_id
        ).order_by(ProjectQuotationCustomerSupport.create_time)
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def add_customer_support(cls, db: AsyncSession, customer_support: ProjectQuotationCustomerSupport) -> ProjectQuotationCustomerSupport:
        """
        添加客服
        
        :param db: 数据库会话
        :param customer_support: 客服对象
        :return: 添加后的客服对象
        """
        db.add(customer_support)
        await db.flush()
        await db.refresh(customer_support)
        return customer_support

    @classmethod
    async def delete_customer_supports_by_quotation_id(cls, db: AsyncSession, project_quotation_id: int) -> bool:
        """
        根据项目报价ID删除所有客服
        
        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 是否成功
        """
        try:
            query = delete(ProjectQuotationCustomerSupport).where(
                ProjectQuotationCustomerSupport.project_quotation_id == project_quotation_id
            )
            await db.execute(query)
            return True
        except Exception:
            return False

    @classmethod
    async def get_customer_support_by_id(cls, db: AsyncSession, customer_support_id: int) -> ProjectQuotationCustomerSupport:
        """
        根据ID获取客服
        
        :param db: 数据库会话
        :param customer_support_id: 客服ID
        :return: 客服对象
        """
        query = select(ProjectQuotationCustomerSupport).where(
            ProjectQuotationCustomerSupport.id == customer_support_id
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @classmethod
    async def delete_customer_support_by_id(cls, db: AsyncSession, customer_support_id: int) -> bool:
        """
        根据ID删除客服
        
        :param db: 数据库会话
        :param customer_support_id: 客服ID
        :return: 是否成功
        """
        try:
            query = delete(ProjectQuotationCustomerSupport).where(
                ProjectQuotationCustomerSupport.id == customer_support_id
            )
            await db.execute(query)
            return True
        except Exception:
            return False

    @classmethod
    async def get_customer_support_ids_by_quotation_id(cls, db: AsyncSession, project_quotation_id: int) -> List[int]:
        """
        根据项目报价ID获取客服ID列表
        
        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 客服ID列表
        """
        query = select(ProjectQuotationCustomerSupport.user_id).where(
            ProjectQuotationCustomerSupport.project_quotation_id == project_quotation_id
        ).order_by(ProjectQuotationCustomerSupport.create_time)
        
        result = await db.execute(query)
        return result.scalars().all()