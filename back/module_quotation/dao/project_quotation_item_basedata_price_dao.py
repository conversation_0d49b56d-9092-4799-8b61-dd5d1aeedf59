"""
项目报价明细基础价目表DAO
"""

from typing import List, Tuple, Optional

from sqlalchemy import select, delete, and_
from sqlalchemy.ext.asyncio import AsyncSession

from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.do.technical_manual_price_do import TechnicalManualPrice
from module_basedata.entity.vo.technical_manual_price_vo import TechnicalManualPriceModel
from module_quotation.entity.do.project_quotation_item_basedata_price_do import ProjectQuotationItemBasedataPrice
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem


class ProjectQuotationItemBasedataPriceDao:
    """
    项目报价明细基础价目表DAO
    """

    @classmethod
    async def get_unique_category_method_list(
        cls, db: AsyncSession, project_quotation_id: int
    ) -> List[Tuple[str, str]]:
        """
        根据项目报价ID获取不重复的(category + method)列表

        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 不重复的(category, method)元组列表
        """
        stmt = (
            select(ProjectQuotationItem.category_code, ProjectQuotationItem.method)
            .where(
                and_(
                    ProjectQuotationItem.project_quotation_id == project_quotation_id,
                )
            )
            .distinct()
        )

        result = await db.execute(stmt)
        return result.all()

    @classmethod
    async def delete_by_project_quotation_id(cls, db: AsyncSession, project_quotation_id: int) -> None:
        """
        根据项目报价ID删除基础价目表数据

        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        """
        stmt = delete(ProjectQuotationItemBasedataPrice).where(
            ProjectQuotationItemBasedataPrice.project_quotation_id == project_quotation_id
        )
        await db.execute(stmt)

    @classmethod
    async def get_technical_manual_price_by_category_method(
        cls, db: AsyncSession, category_code: str, method: str
    ) -> Optional[TechnicalManualPriceModel]:
        """
        根据类别和方法获取技术手册价格信息

        :param db: 数据库会话
        :param category_code: 检测类别的类别编码
        :param method: 检测方法
        :return: 技术手册价格信息
        """
        result = await db.execute(
            select(TechnicalManualPrice)
            .join(TechnicalManualCategory, TechnicalManualPrice.category_code == TechnicalManualCategory.category_code)
            .where(
                and_(
                    TechnicalManualCategory.category_code == category_code,
                    TechnicalManualPrice.method == method,
                )
            )
        )
        price = result.scalar_one_or_none()
        if price:
            return TechnicalManualPriceModel.model_validate(price, from_attributes=True)
        return None

    @classmethod
    async def batch_insert(cls, db: AsyncSession, basedata_prices: List[ProjectQuotationItemBasedataPrice]) -> None:
        """
        批量插入基础价目表数据

        :param db: 数据库会话
        :param basedata_prices: 基础价目表数据列表
        """
        db.add_all(basedata_prices)

    @classmethod
    async def get_by_project_quotation_id(
        cls, db: AsyncSession, project_quotation_id: int
    ) -> List[ProjectQuotationItemBasedataPrice]:
        """
        根据项目报价ID获取基础价目表数据

        :param db: 数据库会话
        :param project_quotation_id: 项目报价ID
        :return: 基础价目表数据列表
        """
        stmt = select(ProjectQuotationItemBasedataPrice).where(
            and_(ProjectQuotationItemBasedataPrice.project_quotation_id == project_quotation_id)
        )
        result = await db.execute(stmt)
        return result.scalars().all()
