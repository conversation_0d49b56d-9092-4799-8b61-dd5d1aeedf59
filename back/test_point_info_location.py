#!/usr/bin/env python3
"""
测试点位信息位置坐标功能
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_point_info_apis():
    """测试点位信息相关API"""
    
    print("🗺️ 测试点位信息位置坐标功能...")
    
    # 使用任务分组ID 18 (之前创建的分组)
    group_id = 18
    
    print(f"\n1. 测试获取分组 {group_id} 的点位信息:")
    
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/point-info/group/{group_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('code') == 200:
                point_info = data.get('data')
                if point_info:
                    print(f"\n   ✅ 点位信息获取成功:")
                    print(f"     点位ID: {point_info.get('id')}")
                    print(f"     点位名称: {point_info.get('pointName')}")
                    print(f"     经度: {point_info.get('longitude')}")
                    print(f"     纬度: {point_info.get('latitude')}")
                    print(f"     海拔: {point_info.get('altitude')}")
                    print(f"     采样时间: {point_info.get('samplingTime')}")
                    return point_info
                else:
                    print(f"   ⚠️  分组 {group_id} 暂无点位信息，将创建新的点位信息")
                    return None
            else:
                print(f"   ❌ API调用失败: {data.get('msg')}")
                return None
        else:
            print(f"   ❌ HTTP请求失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None

def test_create_point_info():
    """测试创建点位信息"""
    
    print(f"\n2. 测试创建点位信息:")
    
    group_id = 18
    
    # 创建点位信息数据
    point_data = {
        "samplingTaskGroupId": group_id,  # 修正字段名
        "pointName": "测试点位-H5位置获取",
        "longitude": 116.397428,  # 北京天安门经度
        "latitude": 39.90923,    # 北京天安门纬度
        "altitude": 44,          # 海拔
        "samplingTime": "2025-09-10 23:30:00",
        "weatherCondition": "晴",
        "temperature": 25.5,
        "humidity": 60,
        "windSpeed": 3.2,
        "windDirection": "东南",
        "remarks": "H5页面位置获取测试点位"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/sampling/point-info/create",
            headers=HEADERS,
            json=point_data,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('code') == 200:
                point_info = data.get('data')
                print(f"\n   ✅ 点位信息创建成功:")
                print(f"     点位ID: {point_info.get('id')}")
                print(f"     点位名称: {point_info.get('pointName')}")
                print(f"     经度: {point_info.get('longitude')}")
                print(f"     纬度: {point_info.get('latitude')}")
                print(f"     海拔: {point_info.get('altitude')}")
                return point_info
            else:
                print(f"   ❌ 创建失败: {data.get('msg')}")
                return None
        else:
            print(f"   ❌ HTTP请求失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None

def test_update_point_info(point_info):
    """测试更新点位信息"""
    
    print(f"\n3. 测试更新点位信息:")
    
    if not point_info or not point_info.get('id'):
        print("   ⚠️  没有可更新的点位信息")
        return False
    
    point_id = point_info.get('id')
    
    # 更新位置坐标
    update_data = {
        "longitude": 116.407526,  # 更新经度
        "latitude": 39.904030,   # 更新纬度
        "altitude": 50,          # 更新海拔
        "temperature": 28.0,     # 更新温度
        "humidity": 65,          # 更新湿度
        "remarks": "H5页面位置获取测试点位 - 已更新坐标"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/sampling/point-info/update/{point_id}",
            headers=HEADERS,
            json=update_data,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('code') == 200:
                updated_point_info = data.get('data')
                print(f"\n   ✅ 点位信息更新成功:")
                print(f"     点位ID: {updated_point_info.get('id')}")
                print(f"     更新后经度: {updated_point_info.get('longitude')}")
                print(f"     更新后纬度: {updated_point_info.get('latitude')}")
                print(f"     更新后海拔: {updated_point_info.get('altitude')}")
                return True
            else:
                print(f"   ❌ 更新失败: {data.get('msg')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def analyze_location_issues():
    """分析位置获取可能的问题"""
    
    print(f"\n4. 分析H5位置获取可能的问题:")
    
    print(f"\n   📱 前端位置获取问题分析:")
    print(f"     1. HTTPS要求: 现代浏览器要求HTTPS才能使用地理位置API")
    print(f"     2. 用户权限: 用户需要授权位置访问权限")
    print(f"     3. 设备支持: 设备需要支持GPS或网络定位")
    print(f"     4. 超时设置: 当前设置10秒超时，可能需要调整")
    print(f"     5. 精度要求: enableHighAccuracy=true可能导致获取时间较长")
    
    print(f"\n   🔧 可能的解决方案:")
    print(f"     1. 检查网站是否使用HTTPS")
    print(f"     2. 提示用户授权位置权限")
    print(f"     3. 增加超时时间或降低精度要求")
    print(f"     4. 添加手动输入坐标的备选方案")
    print(f"     5. 使用IP定位作为备选方案")
    
    print(f"\n   🌐 当前H5页面位置获取配置:")
    print(f"     - enableHighAccuracy: true (高精度)")
    print(f"     - timeout: 10000ms (10秒)")
    print(f"     - maximumAge: 60000ms (缓存1分钟)")

def print_location_fix_suggestions():
    """打印位置获取修复建议"""
    
    print(f"\n🚀 H5位置获取修复建议:")
    
    print(f"\n📋 问题诊断:")
    print(f"   1. 检查浏览器控制台是否有权限错误")
    print(f"   2. 确认网站是否使用HTTPS协议")
    print(f"   3. 检查设备定位服务是否开启")
    print(f"   4. 测试不同浏览器的兼容性")
    
    print(f"\n🔧 代码优化建议:")
    print(f"   1. 增加更详细的错误处理和用户提示")
    print(f"   2. 提供手动输入坐标的选项")
    print(f"   3. 添加IP定位作为备选方案")
    print(f"   4. 优化超时和精度设置")
    
    print(f"\n💡 用户体验改进:")
    print(f"   1. 添加位置获取进度提示")
    print(f"   2. 提供位置权限授权指导")
    print(f"   3. 显示当前位置获取状态")
    print(f"   4. 支持离线模式下的手动输入")

if __name__ == "__main__":
    # 测试现有点位信息
    existing_point_info = test_point_info_apis()
    
    # 如果没有现有点位信息，创建新的
    if not existing_point_info:
        point_info = test_create_point_info()
    else:
        point_info = existing_point_info
    
    # 测试更新点位信息
    if point_info:
        test_update_point_info(point_info)
    
    # 分析位置获取问题
    analyze_location_issues()
    
    # 打印修复建议
    print_location_fix_suggestions()
    
    print("\n🎯 点位信息API测试完成！后端功能正常，问题可能在前端位置获取逻辑。")
