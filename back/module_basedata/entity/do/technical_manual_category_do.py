from datetime import datetime

from sqlalchemy import Column, DateTime, String, BigInteger, Index

from config.database import Base


class TechnicalManualCategory(Base):
    """
    技术手册类目表
    """

    __tablename__ = "technical_manual_category"

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    category_code = Column(String(20), nullable=False, unique=True, comment="类目唯一编号")
    classification = Column(String(50), nullable=False, comment="分类")
    category = Column(String(50), nullable=False, comment="检测类别")

    # 系统字段
    status = Column(String(1), default="0", comment="状态（0正常 1停用）")
    create_by = Column(String(64), default="", comment="创建者")
    create_time = Column(DateTime, default=datetime.now(), comment="创建时间")
    update_by = Column(String(64), default="", comment="更新者")
    update_time = Column(DateTime, default=datetime.now(), comment="更新时间")
    remark = Column(String(500), default="", comment="备注")

    # 创建唯一索引：分类+检测类别组合唯一
    __table_args__ = (Index("idx_technical_manual_category_unique", "classification", "category", unique=True),)
