"""
技术价格导入相关的数据模型
"""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel


class TechnicalManualPriceImportModel(BaseModel):
    """技术价格导入数据模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    method: str = Field(..., description="检测方法描述")
    category: str = Field(..., description="具体检测类别")
    category_code: Optional[str] = Field(None, description="具体检测类别的代码")
    first_item_price: Optional[float] = Field(None, description="检测首项单价", ge=0)
    additional_item_price: Optional[float] = Field(None, description="检测增项单价", ge=0)
    testing_fee_limit: Optional[float] = Field(None, description="检测费上限", ge=0)
    sampling_price: Optional[float] = Field(None, description="采样单价", ge=0)
    pretreatment_price: Optional[float] = Field(None, description="前处理单价", ge=0)


class TechnicalManualPriceImportErrorModel(BaseModel):
    """技术价格导入错误模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    row_number: int
    error_message: str
    method: Optional[str]
    category: Optional[str]
    first_item_price: Optional[float]
    additional_item_price: Optional[float]
    testing_fee_limit: Optional[float]
    sampling_price: Optional[float]
    pretreatment_price: Optional[float]


class TechnicalManualPriceImportResultModel(BaseModel):
    """技术价格导入结果模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    success: bool
    total_count: int
    success_count: int
    error_count: int
    errors: List[TechnicalManualPriceImportErrorModel]
