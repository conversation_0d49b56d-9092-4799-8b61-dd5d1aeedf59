from typing import List, Optional

from fastapi import APIRouter, Depends, Request, Path, Query
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_basedata.entity.vo.technical_manual_category_vo import (
    TechnicalManualCategoryModel,
    TechnicalManualCategoryQueryModel,
)
from module_basedata.service.technical_manual_category_service import TechnicalManualCategoryService
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil

# 创建路由
router = APIRouter(prefix="/basedata/technical-manual-category", tags=["技术手册类目管理"])


@router.get("/list", response_model=List[TechnicalManualCategoryModel], summary="获取技术手册类目列表")
async def get_technical_manual_category_list(
    request: Request,
    query_params: TechnicalManualCategoryQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册类目列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册类目列表
    """
    service = TechnicalManualCategoryService(db)
    category_list = await service.get_category_list(query_params)
    return ResponseUtil.success(data=category_list)


@router.get("/page", response_model=PageResponseModel, summary="获取技术手册类目分页列表")
async def get_technical_manual_category_page(
    request: Request,
    query_params: TechnicalManualCategoryQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册类目分页列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册类目分页列表
    """
    service = TechnicalManualCategoryService(db)
    category_page = await service.get_category_page(query_params)
    return ResponseUtil.success(data=category_page)


@router.get("/{id}", response_model=TechnicalManualCategoryModel, summary="获取技术手册类目详情")
async def get_technical_manual_category_detail(
    request: Request,
    id: int = Path(..., description="技术手册类目ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册类目详情

    :param request: 请求对象
    :param id: 技术手册类目ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册类目详情
    """
    service = TechnicalManualCategoryService(db)
    category = await service.get_category_detail(id)
    return ResponseUtil.success(data=category)


@router.post("", response_model=CrudResponseModel, summary="新增技术手册类目")
async def add_technical_manual_category(
    request: Request,
    category: TechnicalManualCategoryModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增技术手册类目

    :param request: 请求对象
    :param category: 新增技术手册类目对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = TechnicalManualCategoryService(db)
    result = await service.add_category(category, current_user)
    return ResponseUtil.success(data=result)


@router.put("", response_model=CrudResponseModel, summary="编辑技术手册类目")
async def edit_technical_manual_category(
    request: Request,
    category: TechnicalManualCategoryModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑技术手册类目

    :param request: 请求对象
    :param category: 编辑技术手册类目对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = TechnicalManualCategoryService(db)
    result = await service.edit_category(category, current_user)
    return ResponseUtil.success(data=result)


@router.delete("/{id}", response_model=CrudResponseModel, summary="删除技术手册类目")
async def delete_technical_manual_category(
    request: Request,
    id: str = Path(..., description="技术手册类目ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除技术手册类目

    :param request: 请求对象
    :param id: 技术手册类目ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = TechnicalManualCategoryService(db)
    for i in id.strip().split(","):
        result = await service.delete_category(int(i), current_user)
    return ResponseUtil.success(data=result)


@router.get("/options/classifications", summary="获取所有分类")
async def get_classifications(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取所有分类

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 分类列表
    """
    service = TechnicalManualCategoryService(db)
    classifications = await service.get_classifications()
    return ResponseUtil.success(data=classifications)


@router.get("/options/categories", summary="根据分类获取检测类别")
async def get_categories_by_classification(
    request: Request,
    classification: Optional[str] = Query(None, description="分类"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    根据分类获取检测类别

    :param request: 请求对象
    :param classification: 分类
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测类别列表
    """
    service = TechnicalManualCategoryService(db)
    if classification:
        categories = await service.get_categories_by_classification(classification)
    else:
        categories = await service.get_categories()
    return ResponseUtil.success(data=categories)


@router.get("/tree", summary="获取类目树形数据")
async def get_category_tree(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取类目树形数据

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 类目树形数据
    """
    service = TechnicalManualCategoryService(db)
    tree = await service.get_category_tree()
    return ResponseUtil.success(data=tree)


@router.get("/export", summary="导出技术手册类目")
async def export_technical_manual_category(
    request: Request,
    query_params: TechnicalManualCategoryQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    导出技术手册类目

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: Excel文件
    """
    service = TechnicalManualCategoryService(db)
    excel_data = await service.export_categories(query_params)

    from fastapi.responses import StreamingResponse
    import io

    return StreamingResponse(
        io.BytesIO(excel_data),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": 'attachment; filename="technical_manual_categories.xlsx"'},
    )
