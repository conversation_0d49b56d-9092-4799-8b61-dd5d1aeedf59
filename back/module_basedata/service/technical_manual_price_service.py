from datetime import datetime
from typing import Optional, List

from fastapi import Request, Response
from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.do.technical_manual_price_do import TechnicalManualPrice
from module_basedata.entity.vo.technical_manual_price_vo import (
    AddTechnicalManualPriceModel,
    EditTechnicalManualPriceModel,
    TechnicalManualPricePageQueryModel,
    TechnicalManualPriceQueryModel,
)
from utils.common_util import CamelCaseUtil, SqlalchemyUtil
from utils.export_util import ExportUtil
from utils.page_util import PageUtil


class TechnicalManualPriceService(BaseService[TechnicalManualPrice]):
    """
    技术手册价格管理模块服务层
    """

    def __init__(self, db: AsyncSession):
        """
        初始化技术手册价格服务

        :param db: 数据库会话
        """
        super().__init__(TechnicalManualPrice, db)
        self.db = db

    async def _get_category_by_code(self, category_code: str) -> Optional[List[TechnicalManualCategory]]:
        """
        根据类目代码从技术手册类目中获取类目

        :param category_code: 类目代码
        :return: 类目列表
        """
        if not category_code:
            return None
        category_stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.category_code == category_code)
        category_result = await self.db.execute(category_stmt)
        return category_result.scalars().all()

    async def get_technical_manual_price_list(self, query_object: TechnicalManualPriceQueryModel):
        """
        获取技术手册价格列表

        :param query_object: 查询参数对象
        :return: 技术手册价格列表
        """
        # 构建查询条件
        conditions = []

        if query_object.method:
            conditions.append(TechnicalManualPrice.method.like(f"%{query_object.method}%"))
        if query_object.category_code:
            conditions.append(TechnicalManualPrice.category_code == query_object.category_code)

        # 如果有分类或检测类别查询条件，需要先获取对应的类目编号
        if query_object.classification or query_object.category:
            category_codes = []
            if query_object.classification and query_object.category:
                # 精确匹配分类和检测类别
                stmt = select(TechnicalManualCategory.category_code).where(
                    TechnicalManualCategory.classification == query_object.classification,
                    TechnicalManualCategory.category == query_object.category,
                )
                result = await self.db.execute(stmt)
                category_code = result.scalar()
                if category_code:
                    category_codes = [category_code]
            elif query_object.classification:
                # 只有分类，获取该分类下的所有类目编号
                stmt = select(TechnicalManualCategory.category_code).where(
                    TechnicalManualCategory.classification == query_object.classification
                )
                result = await self.db.execute(stmt)
                category_codes = result.scalars().all()
            elif query_object.category:
                # 只有检测类别，获取所有分类下的该检测类别
                stmt = select(TechnicalManualCategory.category_code).where(
                    TechnicalManualCategory.category == query_object.category
                )
                result = await self.db.execute(stmt)
                category_codes = result.scalars().all()

            if category_codes:
                conditions.append(TechnicalManualPrice.category_code.in_(category_codes))
            else:
                # 没有匹配的类目，返回空结果
                return []
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManualPrice.method.like(f"%{query_object.keyword}%"),
                TechnicalManualPrice.remark.like(f"%{query_object.keyword}%"),
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time

            time_condition = and_(
                TechnicalManualPrice.create_time
                >= datetime.combine(datetime.strptime(query_object.begin_time, "%Y-%m-%d"), time(00, 00, 00)),
                TechnicalManualPrice.create_time
                <= datetime.combine(datetime.strptime(query_object.end_time, "%Y-%m-%d"), time(23, 59, 59)),
            )
            conditions.append(time_condition)

        # 执行关联查询
        stmt = (
            select(TechnicalManualPrice, TechnicalManualCategory)
            .join(TechnicalManualCategory, TechnicalManualPrice.category_code == TechnicalManualCategory.category_code)
            .where(and_(*conditions))
            .order_by(TechnicalManualPrice.method, TechnicalManualCategory.category)
        )
        prices = await self.db.execute(stmt).all()

        # 组装结果，包含关联的分类和检测类别信息
        technical_prices = []
        for price in prices:
            price_dict = SqlalchemyUtil.base_to_dict(price)
            categories = await self._get_category_by_code(price.category_code)
            if categories:
                price_dict["classification"] = categories[0].classification
                price_dict["category"] = ",".join([category.category for category in categories])
            technical_prices.append(price_dict)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_prices)

    async def get_technical_manual_price_page(self, query_object: TechnicalManualPricePageQueryModel):
        """
        获取技术手册价格分页列表

        :param query_object: 查询参数对象
        :return: 技术手册价格分页列表
        """
        # 构建查询条件
        conditions = []

        if query_object.method:
            conditions.append(TechnicalManualPrice.method.like(f"%{query_object.method}%"))
        if query_object.category_code:
            conditions.append(TechnicalManualPrice.category_code == query_object.category_code)

        # 如果有分类或检测类别查询条件，需要先获取对应的类目编号
        if query_object.classification or query_object.category:
            category_code = []
            if query_object.classification and query_object.category:
                # 精确匹配分类和检测类别
                stmt = select(TechnicalManualCategory.category_code).where(
                    TechnicalManualCategory.classification == query_object.classification,
                    TechnicalManualCategory.category == query_object.category,
                )
                result = await self.db.execute(stmt)
                category_code = result.scalar()
                if category_code:
                    category_code = [category_code]
            elif query_object.classification:
                # 只有分类，获取该分类下的所有类目编号
                stmt = select(TechnicalManualCategory.category_code).where(
                    TechnicalManualCategory.classification == query_object.classification
                )
                result = await self.db.execute(stmt)
                category_code = result.scalars().all()
            elif query_object.category:
                # 只有检测类别，获取所有分类下的该检测类别
                stmt = select(TechnicalManualCategory.category_code).where(
                    TechnicalManualCategory.category == query_object.category
                )
                result = await self.db.execute(stmt)
                category_code = result.scalars().all()

            if category_code:
                conditions.append(TechnicalManualPrice.category_code.in_(category_code))
            else:
                # 没有匹配的类目，返回空结果
                from utils.page_util import PageResponseModel

                return PageResponseModel(
                    total=0, rows=[], page_num=query_object.page_num, page_size=query_object.page_size
                )
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManualPrice.method.like(f"%{query_object.keyword}%"),
                TechnicalManualPrice.remark.like(f"%{query_object.keyword}%"),
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time

            time_condition = and_(
                TechnicalManualPrice.create_time
                >= datetime.combine(datetime.strptime(query_object.begin_time, "%Y-%m-%d"), time(00, 00, 00)),
                TechnicalManualPrice.create_time
                <= datetime.combine(datetime.strptime(query_object.end_time, "%Y-%m-%d"), time(23, 59, 59)),
            )
            conditions.append(time_condition)

        # 执行关联查询
        stmt = select(TechnicalManualPrice).where(and_(*conditions)).order_by(TechnicalManualPrice.method)
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)
        # 组装结果，包含关联的分类和检测类别信息
        for price_dict in page_result.rows:
            category_code = price_dict.get("categoryCode") or price_dict.get("category_code")
            categories = await self._get_category_by_code(category_code)
            if categories:
                price_dict["classification"] = categories[0].classification
                price_dict["category"] = ",".join([category.category for category in categories])

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def get_technical_manual_price_detail(self, id: int):
        """
        获取技术手册价格详情

        :param id: 技术手册价格ID
        :return: 技术手册价格详情
        """
        stmt = select(TechnicalManualPrice).where(TechnicalManualPrice.id == id)
        result = await self.db.execute(stmt)
        row = result.first()
        if not row:
            raise ServiceException(message=f"技术手册价格ID：{id}不存在")
        technical_manual_price = row[0]
        # 组装结果，包含关联的分类和检测类别信息
        price_dict = SqlalchemyUtil.base_to_dict(technical_manual_price)
        categories = await self._get_category_by_code(technical_manual_price.category_code)
        if categories:
            price_dict["classification"] = categories[0].classification
            price_dict["category"] = ",".join([category.category for category in categories])

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(price_dict)

    async def check_technical_manual_price_unique(self, method: str, category_code: str, id: int = None):
        """
        检查技术手册价格是否唯一

        :param method: 检测方法
        :param category_code: 类目编号
        :param id: 技术手册价格ID
        :return: 是否唯一
        """
        conditions = [
            TechnicalManualPrice.method == method,
            TechnicalManualPrice.category_code == category_code,
        ]

        if id:
            conditions.append(TechnicalManualPrice.id != id)

        stmt = select(TechnicalManualPrice).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def get_method_category_options(self):
        """
        获取检测方法和类别选项

        :return: 检测方法和类别选项列表
        """
        # 从技术手册中获取不重复的检测方法
        stmt = (
            select(TechnicalManual.method, TechnicalManual.category_codes)
            .where(TechnicalManual.status == "0")
            .group_by(TechnicalManual.method, TechnicalManual.category_codes)
            .order_by(TechnicalManual.method)
        )
        result = await self.db.execute(stmt)
        method_category_data = result.all()

        # 为每个检测方法获取对应的检测类别和分类
        method_category_options = []
        for method, category_codes in method_category_data:
            # 获取该检测方法对应的检测类别和分类
            category_stmt = (
                select(
                    TechnicalManualCategory.category_code,
                    TechnicalManualCategory.category,
                    TechnicalManualCategory.classification,
                )
                .where(TechnicalManualCategory.category_code.in_(category_codes))
                .order_by(TechnicalManualCategory.category)
            )

            category_result = await self.db.execute(category_stmt)
            categories_data = category_result.all()

            # 构建类别列表
            categories = []
            seen = set()
            for category_row in categories_data:
                category_code = category_row[0]
                category = category_row[1]
                classification = category_row[2] or ""
                # 显示格式：[分类]检测类别
                display_text = f"[{classification}]{category}" if classification else category
                if display_text not in seen:
                    seen.add(display_text)
                    categories.append(
                        {
                            "category_code": category_code,
                            "category": category,
                            "classification": classification,
                            "displayText": display_text,
                        }
                    )

            if categories:  # 只有当该方法有对应的类别时才添加
                method_category_options.append({"method": method, "categories": categories})

        return method_category_options

    async def add_technical_manual_price(
        self, _: Request, technical_manual_price_model: AddTechnicalManualPriceModel, current_user: CurrentUserModel
    ):
        """
        新增技术手册价格

        :param request: 请求对象
        :param technical_manual_price_model: 新增技术手册价格对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 新增结果
        """
        try:
            # 校验技术手册价格是否唯一
            if not await self.check_technical_manual_price_unique(
                technical_manual_price_model.method, technical_manual_price_model.category_code
            ):
                raise ServiceException(message=f"新增技术手册价格失败，该检测方法和类目组合已存在")

            # 创建技术手册价格对象
            technical_manual_price = TechnicalManualPrice(
                method=technical_manual_price_model.method,
                category_code=technical_manual_price_model.category_code,
                # 检测价格相关字段
                first_item_price=technical_manual_price_model.first_item_price,
                additional_item_price=technical_manual_price_model.additional_item_price,
                testing_fee_limit=technical_manual_price_model.testing_fee_limit,
                # 采集价格相关字段
                sampling_price=technical_manual_price_model.sampling_price,
                pretreatment_price=technical_manual_price_model.pretreatment_price,
                # 其他价格相关字段
                # special_consumables_price=technical_manual_price_model.special_consumables_price,
                # 系统字段
                status=technical_manual_price_model.status,
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=technical_manual_price_model.remark,
            )

            # 新增技术手册价格
            self.db.add(technical_manual_price)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="新增成功", result={"id": technical_manual_price.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("添加技术手册价格失败", e)
            # raise ServiceException(message=f"新增技术手册价格失败: {str(e)}")
            raise

    async def edit_technical_manual_price(
        self, _: Request, technical_manual_price_model: EditTechnicalManualPriceModel, current_user: CurrentUserModel
    ):
        """
        编辑技术手册价格

        :param request: 请求对象
        :param technical_manual_price_model: 编辑技术手册价格对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 编辑结果
        """
        try:
            # 获取技术手册价格对象
            stmt = select(TechnicalManualPrice).where(TechnicalManualPrice.id == technical_manual_price_model.id)
            result = await self.db.execute(stmt)
            technical_manual_price = result.scalars().first()
            if not technical_manual_price:
                raise ServiceException(message=f"技术手册价格ID：{technical_manual_price_model.id}不存在")

            # 校验技术手册价格是否唯一
            if not await self.check_technical_manual_price_unique(
                technical_manual_price_model.method,
                technical_manual_price_model.category_code,
                technical_manual_price_model.id,
            ):
                raise ServiceException(message=f"修改技术手册价格失败，该检测方法和类目组合已存在")

            # 更新技术手册价格对象
            technical_manual_price.method = technical_manual_price_model.method
            technical_manual_price.category_code = technical_manual_price_model.category_code
            # 检测价格相关字段
            technical_manual_price.first_item_price = technical_manual_price_model.first_item_price
            technical_manual_price.additional_item_price = technical_manual_price_model.additional_item_price
            technical_manual_price.testing_fee_limit = technical_manual_price_model.testing_fee_limit
            # 采集价格相关字段
            technical_manual_price.sampling_price = technical_manual_price_model.sampling_price
            technical_manual_price.pretreatment_price = technical_manual_price_model.pretreatment_price
            # 其他价格相关字段 去掉
            # technical_manual_price.special_consumables_price = technical_manual_price_model.special_consumables_price
            # 系统字段
            technical_manual_price.status = technical_manual_price_model.status
            technical_manual_price.update_by = current_user.user.user_name if current_user and current_user.user else ""
            technical_manual_price.update_time = datetime.now()
            technical_manual_price.remark = technical_manual_price_model.remark

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="修改成功", result={"id": technical_manual_price.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("修改技术手册价格失败", e)
            # raise ServiceException(message=f"修改技术手册价格失败: {str(e)}")
            raise

    async def delete_technical_manual_price(self, id: int, current_user: CurrentUserModel):
        """
        删除技术手册价格

        :param id: 技术手册价格ID
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 删除结果
        """
        try:
            # 检查技术手册价格是否存在
            stmt = select(TechnicalManualPrice).where(TechnicalManualPrice.id == id)
            result = await self.db.execute(stmt)
            technical_manual_price = result.scalars().first()
            if not technical_manual_price:
                raise ServiceException(message=f"技术手册价格ID：{id}不存在")

            # 真删除技术手册价格
            await self.db.delete(technical_manual_price)

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="删除成功", result={"id": id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("删除技术手册价格失败", e)
            raise ServiceException(message=f"删除技术手册价格失败: {str(e)}")

    async def export_technical_manual_price(
        self, query_object: TechnicalManualPriceQueryModel, export_type: str
    ) -> Response:
        """
        导出技术手册价格

        :param query_object: 查询参数对象
        :param export_type: 导出类型（excel, pdf, word）
        :return: 导出文件响应
        """
        # 获取技术手册价格列表
        technical_manual_prices = await self.get_technical_manual_price_list(query_object)

        # 转换为字典列表
        data = []
        for price in technical_manual_prices:
            # 将SQLAlchemy模型转换为字典
            categories = await self._get_category_by_code(price.category_code)
            price_dict = {
                "id": price.id,
                "method": price.method,
                "category": categories[0].category if categories else "",
                "classification": categories[0].classification if categories else "",
                "first_item_price": str(price.first_item_price) if price.first_item_price else "",
                "additional_item_price": str(price.additional_item_price) if price.additional_item_price else "",
                "testing_fee_limit": str(price.testing_fee_limit) if price.testing_fee_limit else "",
                "status": "启用" if price.status == "0" else "停用",
                "create_time": price.create_time.strftime("%Y-%m-%d %H:%M:%S") if price.create_time else "",
                "create_by": price.create_by,
                "remark": price.remark or "",
            }
            data.append(price_dict)

        # 定义表头映射
        headers = {
            "id": "ID",
            "method": "检测方法",
            "category": "检测类别",
            "classification": "分类",
            "first_item_price": "检测首项单价",
            "additional_item_price": "检测增项单价",
            "testing_fee_limit": "检测费上限",
            "status": "状态",
            "create_time": "创建时间",
            "create_by": "创建人",
            "remark": "备注",
        }

        # 导出文件
        return ExportUtil.export_data(data, headers, "技术手册价格", export_type)
