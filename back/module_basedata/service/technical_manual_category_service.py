"""
技术手册类目异步服务
"""

import io
from datetime import datetime
from typing import List

import pandas as pd
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.vo.technical_manual_category_vo import (
    TechnicalManualCategoryModel,
    TechnicalManualCategoryQueryModel,
)
from utils.common_util import CamelCaseUtil
from utils.page_util import PageUtil


class TechnicalManualCategoryService:
    """
    技术手册类目异步服务
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_category_list(
        self, query_object: TechnicalManualCategoryQueryModel
    ) -> List[TechnicalManualCategoryModel]:
        """
        获取技术手册类目列表

        :param query_object: 查询参数对象
        :return: 技术手册类目列表
        """
        # 构建查询条件
        conditions = []

        if query_object.classification:
            conditions.append(TechnicalManualCategory.classification.like(f"%{query_object.classification}%"))
        if query_object.category:
            conditions.append(TechnicalManualCategory.category.like(f"%{query_object.category}%"))
        if query_object.category_code:
            conditions.append(TechnicalManualCategory.category_code == query_object.category_code)
        if query_object.status:
            conditions.append(TechnicalManualCategory.status == query_object.status)

        # 执行查询
        stmt = (
            select(TechnicalManualCategory)
            .where(and_(*conditions))
            .order_by(TechnicalManualCategory.classification, TechnicalManualCategory.category)
        )
        result = await self.db.execute(stmt)
        category_list = result.scalars().all()

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(category_list)

    async def get_category_page(self, query_object: TechnicalManualCategoryQueryModel):
        """
        获取技术手册类目分页列表

        :param query_object: 查询参数对象
        :return: 技术手册类目分页列表
        """
        # 构建查询条件
        conditions = []

        if query_object.classification:
            conditions.append(TechnicalManualCategory.classification.like(f"%{query_object.classification}%"))
        if query_object.category:
            conditions.append(TechnicalManualCategory.category.like(f"%{query_object.category}%"))
        if query_object.category_code:
            conditions.append(TechnicalManualCategory.category_code == query_object.category_code)
        if query_object.status:
            conditions.append(TechnicalManualCategory.status == query_object.status)

        # 执行查询
        stmt = (
            select(TechnicalManualCategory)
            .where(and_(*conditions))
            .order_by(
                TechnicalManualCategory.create_time.desc(),
                TechnicalManualCategory.classification,
                TechnicalManualCategory.category,
            )
        )
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def get_category_detail(self, id: int) -> TechnicalManualCategoryModel:
        """
        获取技术手册类目详情

        :param id: 技术手册类目ID
        :return: 技术手册类目详情
        """
        stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.id == id)
        result = await self.db.execute(stmt)
        category = result.scalars().first()

        if not category:
            raise ServiceException(message=f"技术手册类目ID：{id}不存在")

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(category)

    async def add_category(self, category_model: TechnicalManualCategoryModel, current_user: CurrentUserModel):
        """
        新增技术手册类目

        :param category_model: 新增技术手册类目对象
        :param current_user: 当前用户对象
        :return: 新增结果
        """
        try:
            # 校验分类和检测类别组合是否唯一
            if not await self.check_category_unique(category_model.classification, category_model.category):
                raise ServiceException(message=f"新增技术手册类目失败，该分类和检测类别组合已存在")

            # 生成类目编号
            category_code = await self.generate_category_code()

            # 创建技术手册类目对象
            category = TechnicalManualCategory(
                category_code=category_code,
                classification=category_model.classification,
                category=category_model.category,
                status=category_model.status or "0",
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=category_model.remark,
            )

            # 新增技术手册类目
            self.db.add(category)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="新增成功", result={"id": category.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 重新抛出异常
            raise

    async def edit_category(self, category_model: TechnicalManualCategoryModel, current_user: CurrentUserModel):
        """
        编辑技术手册类目

        :param category_model: 编辑技术手册类目对象
        :param current_user: 当前用户对象
        :return: 编辑结果
        """
        try:
            # 获取技术手册类目对象
            stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.id == category_model.id)
            result = await self.db.execute(stmt)
            category = result.scalars().first()
            if not category:
                raise ServiceException(message=f"技术手册类目ID：{category_model.id}不存在")

            # 校验分类和检测类别组合是否唯一
            if not await self.check_category_unique(
                category_model.classification, category_model.category, category_model.id
            ):
                raise ServiceException(message=f"修改技术手册类目失败，该分类和检测类别组合已存在")

            # 更新技术手册类目对象
            category.classification = category_model.classification
            category.category = category_model.category
            category.status = category_model.status
            category.update_by = current_user.user.user_name if current_user and current_user.user else ""
            category.update_time = datetime.now()
            category.remark = category_model.remark

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="修改成功", result={"id": category.id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 重新抛出异常
            raise

    async def delete_category(self, id: int, current_user: CurrentUserModel):
        """
        删除技术手册类目

        :param id: 技术手册类目ID
        :param current_user: 当前用户对象
        :return: 删除结果
        """
        try:
            # 检查技术手册类目是否存在
            stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.id == id)
            result = await self.db.execute(stmt)
            category = result.scalars().first()
            if not category:
                raise ServiceException(message=f"技术手册类目ID：{id}不存在")

            # 真删除技术手册类目
            await self.db.delete(category)

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(is_success=True, message="删除成功", result={"id": id})
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 重新抛出异常
            raise

    async def check_category_unique(self, classification: str, category: str, id: int = None):
        """
        检查技术手册类目是否唯一

        :param classification: 分类
        :param category: 检测类别
        :param id: 技术手册类目ID
        :return: 是否唯一
        """
        conditions = [
            TechnicalManualCategory.classification == classification,
            TechnicalManualCategory.category == category,
        ]

        if id:
            conditions.append(TechnicalManualCategory.id != id)

        stmt = select(TechnicalManualCategory).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def generate_category_code(self) -> str:
        """
        生成类目编号

        :return: 类目编号
        """
        # 查询最大的类目编号
        stmt = select(func.max(TechnicalManualCategory.category_code)).where(
            TechnicalManualCategory.category_code.like("CATE%")
        )
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        if max_code:
            # 提取数字部分并加1
            num = int(max_code[4:]) + 1
        else:
            num = 1

        return f"CATE{num:05d}"

    async def get_classifications(self) -> List[str]:
        """
        获取所有分类

        :return: 分类列表
        """
        stmt = (
            select(TechnicalManualCategory.classification).distinct().order_by(TechnicalManualCategory.classification)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_categories_by_classification(self, classification: str) -> List[str]:
        """
        根据分类获取检测类别

        :param classification: 分类
        :return: 检测类别列表
        """
        stmt = (
            select(TechnicalManualCategory.category)
            .where(TechnicalManualCategory.classification == classification)
            .distinct()
            .order_by(TechnicalManualCategory.category)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_categories(self) -> List[str]:
        """
        获取所有检测类别

        :return: 检测类别列表
        """
        stmt = select(TechnicalManualCategory.category).distinct().order_by(TechnicalManualCategory.category)
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_category_tree(self):
        """
        获取类目树形数据

        :return: 类目树形数据
        """
        # 获取所有类目数据
        stmt = select(TechnicalManualCategory).order_by(
            TechnicalManualCategory.classification, TechnicalManualCategory.category
        )
        result = await self.db.execute(stmt)
        categories = result.scalars().all()

        # 构建树形结构
        tree = {}
        for category in categories:
            if category.classification not in tree:
                tree[category.classification] = {
                    "label": category.classification,
                    "value": category.classification,
                    "children": [],
                }

            tree[category.classification]["children"].append(
                {
                    "label": category.category,
                    "value": category.category_code,
                    "categoryCode": category.category_code,
                    "classification": category.classification,
                    "category": category.category,
                }
            )

        return list(tree.values())

    async def export_categories(self, query_object: TechnicalManualCategoryQueryModel) -> bytes:
        """
        导出技术手册类目

        :param query_object: 查询参数对象
        :return: Excel文件字节数据
        """
        # 获取数据
        categories = await self.get_category_list(query_object)

        # 转换为DataFrame
        data = []
        for category in categories:
            data.append(
                {
                    "类目编号": category.get("categoryCode"),
                    "分类": category.get("classification"),
                    "检测类别": category.get("category"),
                    "状态": "正常" if category.get("status") == "0" else "停用",
                    "创建时间": category.get("createTime"),
                    "备注": category.get("remark") or "",
                }
            )

        df = pd.DataFrame(data)

        # 保存到字节流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="技术手册类目", index=False)

        output.seek(0)
        return output.getvalue()
