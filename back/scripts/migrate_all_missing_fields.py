#!/usr/bin/env python3
"""
迁移所有缺失字段的综合脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from config.env import DataBaseConfig


async def check_and_migrate_all_fields():
    """
    检查并迁移所有缺失的字段
    """
    print("开始检查并迁移所有缺失的字段...")
    
    try:
        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"
        
        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=False)  # 关闭详细日志
        
        # 定义需要检查的字段
        fields_to_check = [
            {
                "table": "project_quotation",
                "field": "business_type",
                "sql": "ALTER TABLE project_quotation ADD COLUMN business_type VARCHAR(50) DEFAULT 'sampling' COMMENT '业务类型：sampling-一般采样，sample-送样';",
                "update_sql": "UPDATE project_quotation SET business_type = 'sampling' WHERE business_type IS NULL;",
                "index_sql": "CREATE INDEX idx_project_quotation_business_type ON project_quotation(business_type);"
            },
            {
                "table": "technical_manual_price",
                "field": "category_code",
                "sql": "ALTER TABLE technical_manual_price ADD COLUMN category_code VARCHAR(100) COMMENT '类别代码，关联技术手册类别表';",
                "update_sql": None,
                "index_sql": "CREATE INDEX idx_technical_manual_price_category_code ON technical_manual_price(category_code);"
            }
        ]
        
        async with engine.begin() as conn:
            for field_info in fields_to_check:
                table = field_info["table"]
                field = field_info["field"]
                
                print(f"\n检查 {table}.{field} 字段...")
                
                # 检查字段是否存在
                check_sql = f"""
                SELECT COUNT(*) as field_exists
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = '{table}' 
                AND COLUMN_NAME = '{field}';
                """
                
                result = await conn.execute(text(check_sql))
                field_exists = result.fetchone()[0]
                
                if field_exists > 0:
                    print(f"✅ {table}.{field} 字段已存在")
                    continue
                
                print(f"📝 添加 {table}.{field} 字段...")
                await conn.execute(text(field_info["sql"]))
                print(f"✅ {table}.{field} 字段添加成功")
                
                # 更新现有记录（如果有更新SQL）
                if field_info["update_sql"]:
                    print(f"📝 更新 {table} 表的现有记录...")
                    await conn.execute(text(field_info["update_sql"]))
                    print(f"✅ {table} 表的现有记录更新成功")
                
                # 添加索引
                if field_info["index_sql"]:
                    print(f"📝 为 {table}.{field} 添加索引...")
                    try:
                        await conn.execute(text(field_info["index_sql"]))
                        print(f"✅ {table}.{field} 索引添加成功")
                    except Exception as e:
                        if "Duplicate key name" in str(e):
                            print(f"⚠️  {table}.{field} 索引已存在，跳过")
                        else:
                            print(f"⚠️  {table}.{field} 索引添加失败: {e}")
        
        await engine.dispose()
        print("\n🎉 所有字段迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def verify_database_schema():
    """
    验证数据库模式
    """
    print("验证数据库模式...")
    
    try:
        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"
        
        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=False)
        
        # 检查关键表和字段
        checks = [
            ("project_quotation", "business_type"),
            ("technical_manual_price", "category_code"),
            ("project_quotation_approval_record", "project_quotation_id"),
            ("project_quotation_approval_record", "approver_type"),
            ("project_quotation_approval_record", "approval_status"),
        ]
        
        async with engine.begin() as conn:
            print("\n数据库模式验证结果:")
            print("-" * 50)
            
            for table, field in checks:
                check_sql = f"""
                SELECT COUNT(*) as field_exists
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = '{table}' 
                AND COLUMN_NAME = '{field}';
                """
                
                result = await conn.execute(text(check_sql))
                field_exists = result.fetchone()[0]
                
                status = "✅ 存在" if field_exists > 0 else "❌ 缺失"
                print(f"{table}.{field}: {status}")
            
            # 检查审批记录表是否存在
            table_check_sql = """
            SELECT COUNT(*) as table_exists
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'project_quotation_approval_record';
            """
            
            result = await conn.execute(text(table_check_sql))
            table_exists = result.fetchone()[0]
            
            print(f"\nproject_quotation_approval_record 表: {'✅ 存在' if table_exists > 0 else '❌ 缺失'}")
            
            if table_exists > 0:
                # 检查表中的记录数
                count_sql = "SELECT COUNT(*) FROM project_quotation_approval_record;"
                result = await conn.execute(text(count_sql))
                record_count = result.fetchone()[0]
                print(f"审批记录数: {record_count}")
        
        await engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """
    主函数
    """
    if len(sys.argv) > 1 and sys.argv[1] == "verify":
        success = await verify_database_schema()
    else:
        success = await check_and_migrate_all_fields()
        if success:
            await verify_database_schema()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    print("数据库字段迁移和验证工具")
    print("用法:")
    print("  python migrate_all_missing_fields.py        # 执行所有迁移")
    print("  python migrate_all_missing_fields.py verify # 仅验证数据库模式")
    print()
    
    asyncio.run(main())
