#!/usr/bin/env python3
"""
清理导入的客户数据
删除之前导入的客户数据，以便重新导入
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from config.database import AsyncSessionLocal
from utils.log_util import logger


async def clean_imported_data():
    """清理导入的数据"""
    async with AsyncSessionLocal() as db:
        try:
            # 删除客户内部负责人关联
            await db.execute(text("DELETE FROM customer_internal_manager WHERE create_by = 'system'"))

            # 删除客户联系人
            await db.execute(text("DELETE FROM customer_contact WHERE create_by = 'system'"))

            # 先删除子客户（公司级别），再删除父客户（集团级别）
            await db.execute(text("DELETE FROM customer WHERE create_by = 'system' AND customer_level = '2' AND customer_id >= 18"))
            await db.execute(text("DELETE FROM customer WHERE create_by = 'system' AND customer_level = '1' AND customer_id >= 18"))

            await db.commit()
            logger.info("成功清理导入的客户数据")
            print("✅ 成功清理导入的客户数据")

        except Exception as e:
            await db.rollback()
            logger.error(f"清理数据失败: {e}")
            print(f"❌ 清理数据失败: {e}")


if __name__ == "__main__":
    asyncio.run(clean_imported_data())
