#!/usr/bin/env python3
"""
修复联系人手机号格式的脚本
将12345678911.0格式的手机号修复为12345678911
"""

import asyncio
import os
import sys
import re

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from config.database import AsyncSessionLocal
from utils.log_util import logger


async def fix_phone_format():
    """修复手机号格式"""
    async with AsyncSessionLocal() as db:
        try:
            # 查找所有包含.0的手机号
            result = await db.execute(
                text("SELECT contact_id, phone FROM customer_contact WHERE phone LIKE '%.0'")
            )
            contacts = result.fetchall()
            
            if not contacts:
                print("没有发现需要修复的手机号")
                return
            
            print(f"发现{len(contacts)}个需要修复的手机号")
            
            fixed_count = 0
            for contact in contacts:
                contact_id = contact.contact_id
                old_phone = contact.phone
                
                # 移除.0后缀
                if old_phone.endswith('.0'):
                    new_phone = old_phone[:-2]
                    
                    # 验证是否为有效的手机号（11位数字）
                    if re.match(r'^\d{11}$', new_phone):
                        await db.execute(
                            text("UPDATE customer_contact SET phone = :new_phone WHERE contact_id = :contact_id"),
                            {"new_phone": new_phone, "contact_id": contact_id}
                        )
                        print(f"修复手机号: {old_phone} -> {new_phone}")
                        fixed_count += 1
                    else:
                        print(f"跳过无效手机号: {old_phone}")
            
            await db.commit()
            print(f"✅ 成功修复了{fixed_count}个手机号")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"修复手机号失败: {e}")
            print(f"❌ 修复手机号失败: {e}")


if __name__ == "__main__":
    asyncio.run(fix_phone_format())
