#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部门负责人表迁移脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.get_db import get_db
from sqlalchemy import text

async def migrate_dept_leaders():
    """
    执行部门负责人表迁移
    """
    try:
        # 获取数据库连接
        async for db in get_db():
            print("开始执行部门负责人表迁移...")
            
            # 创建部门负责人表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS sys_dept_leader (
                id SERIAL PRIMARY KEY,
                dept_id BIGINT NOT NULL,
                user_id BIGINT NOT NULL,
                create_by VARCHAR(64) DEFAULT 'system',
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_by VARCHAR(64) DEFAULT 'system',
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (dept_id) REFERENCES sys_dept(dept_id) ON DELETE CASCADE,
                UNIQUE(dept_id, user_id)
            );
            """
            
            await db.execute(text(create_table_sql))
            print("✓ 部门负责人表创建成功")
            
            # 迁移现有数据
            migrate_data_sql = """
            INSERT INTO sys_dept_leader (dept_id, user_id, create_by, create_time, update_by, update_time)
            SELECT 
                dept_id,
                leader,
                'system',
                CURRENT_TIMESTAMP,
                'system',
                CURRENT_TIMESTAMP
            FROM sys_dept 
            WHERE leader IS NOT NULL AND leader > 0 AND del_flag = '0'
            ON CONFLICT (dept_id, user_id) DO NOTHING;
            """
            
            result = await db.execute(text(migrate_data_sql))
            await db.commit()
            print(f"✓ 数据迁移完成，迁移了 {result.rowcount} 条记录")
            
            # 验证迁移结果
            verify_sql = "SELECT COUNT(*) as count FROM sys_dept_leader"
            result = await db.execute(text(verify_sql))
            count = result.scalar()
            print(f"✓ 验证完成，sys_dept_leader表中共有 {count} 条记录")
            
            print("\n迁移完成！")
            print("注意：原有的leader字段已保留，待确认迁移成功后可手动删除")
            
            break
            
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        raise e

if __name__ == "__main__":
    asyncio.run(migrate_dept_leaders())