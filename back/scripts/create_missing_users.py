#!/usr/bin/env python3
"""
创建缺失用户的脚本
专门用于创建Excel中提到但系统中不存在的负责人用户
"""

import asyncio
import os
import sys
import pandas as pd
import hashlib
from datetime import datetime
from typing import Set

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from config.database import AsyncSessionLocal
from utils.log_util import logger


async def get_existing_users(db: AsyncSession) -> dict:
    """获取现有用户"""
    result = await db.execute(
        text("SELECT user_id, nick_name FROM sys_user WHERE del_flag = '0'")
    )
    users = result.fetchall()
    return {user.nick_name: user.user_id for user in users}


async def get_managers_from_excel(excel_file: str) -> Set[str]:
    """从Excel文件中提取负责人姓名"""
    df = pd.read_excel(excel_file)
    managers = set()
    for _, row in df.iterrows():
        manager = row.get('负责人')
        if manager and pd.notna(manager) and manager.strip():
            managers.add(manager.strip())
    return managers


async def create_user(db: AsyncSession, manager_name: str) -> bool:
    """创建单个用户"""
    try:
        # 简单的密码加密（使用MD5）
        password_hash = hashlib.md5("123456".encode()).hexdigest()
        
        # 检查用户名是否已存在
        check_result = await db.execute(
            text("SELECT user_id FROM sys_user WHERE user_name = :user_name OR nick_name = :nick_name"),
            {"user_name": manager_name, "nick_name": manager_name}
        )
        existing = check_result.fetchone()
        
        if existing:
            logger.info(f"用户已存在: {manager_name}")
            return True
        
        # 插入新用户
        insert_sql = text("""
            INSERT INTO sys_user (
                user_name, nick_name, password, email, phonenumber, 
                sex, status, del_flag, create_by, create_time, 
                update_by, update_time
            ) VALUES (
                :user_name, :nick_name, :password, :email, :phonenumber,
                :sex, :status, :del_flag, :create_by, :create_time,
                :update_by, :update_time
            )
        """)
        
        await db.execute(insert_sql, {
            "user_name": manager_name,
            "nick_name": manager_name,
            "password": password_hash,
            "email": f"{manager_name}@company.com",
            "phonenumber": "",
            "sex": "0",
            "status": "0",
            "del_flag": "0",
            "create_by": "system",
            "create_time": datetime.now(),
            "update_by": "system",
            "update_time": datetime.now()
        })
        
        # 验证用户是否创建成功
        user_id_result = await db.execute(
            text("SELECT user_id FROM sys_user WHERE user_name = :user_name"),
            {"user_name": manager_name}
        )
        new_user = user_id_result.fetchone()
        
        if new_user:
            logger.info(f"成功创建用户: {manager_name} (ID: {new_user.user_id})")
            return True
        else:
            logger.error(f"创建用户后无法找到用户: {manager_name}")
            return False
            
    except Exception as e:
        logger.error(f"创建用户{manager_name}时发生异常: {e}")
        return False


async def main():
    """主函数"""
    excel_file = "docs/客户示例.xlsx"
    
    if not os.path.exists(excel_file):
        logger.error(f"Excel文件不存在: {excel_file}")
        return
    
    async with AsyncSessionLocal() as db:
        try:
            # 获取现有用户
            existing_users = await get_existing_users(db)
            logger.info(f"系统中现有用户数量: {len(existing_users)}")
            
            # 从Excel中提取负责人
            managers = await get_managers_from_excel(excel_file)
            logger.info(f"Excel中发现的负责人: {managers}")
            
            # 找出需要创建的用户
            missing_managers = managers - set(existing_users.keys())
            logger.info(f"需要创建的用户: {missing_managers}")
            
            if not missing_managers:
                logger.info("所有负责人都已存在，无需创建新用户")
                return
            
            # 创建缺失的用户
            created_count = 0
            for manager in missing_managers:
                if await create_user(db, manager):
                    created_count += 1
            
            await db.commit()
            logger.info(f"成功创建了 {created_count} 个用户")
            print(f"✅ 成功创建了 {created_count} 个用户")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"创建用户失败: {e}")
            print(f"❌ 创建用户失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
