#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库同步脚本（简化版）
使用配置文件进行数据库同步
"""

import sys
import os
from mysql_sync import MySQLSyncTool
from sync_config import SOURCE_DB_CONFIG, TARGET_DB_CONFIG, SYNC_OPTIONS
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('../logs/mysql_sync_simple.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def main():
    """
    使用配置文件进行数据库同步的主函数
    """
    try:
        logger.info("开始使用配置文件进行数据库同步...")
        
        # 显示配置信息
        logger.info(f"源数据库: {SOURCE_DB_CONFIG['host']}:{SOURCE_DB_CONFIG['port']}/{SOURCE_DB_CONFIG['database']}")
        logger.info(f"目标数据库: {TARGET_DB_CONFIG['host']}:{TARGET_DB_CONFIG['port']}/{TARGET_DB_CONFIG['database']}")
        
        # 确认同步操作
        print("\n警告：此操作将完全清空目标数据库并重新同步所有数据！")
        print(f"源数据库: {SOURCE_DB_CONFIG['host']}:{SOURCE_DB_CONFIG['port']}/{SOURCE_DB_CONFIG['database']}")
        print(f"目标数据库: {TARGET_DB_CONFIG['host']}:{TARGET_DB_CONFIG['port']}/{TARGET_DB_CONFIG['database']}")
        
        confirm = input("\n确认继续吗？(输入 'yes' 继续): ")
        if confirm.lower() != 'yes':
            logger.info("用户取消同步操作")
            return
        
        # 创建同步工具并执行同步
        sync_tool = MySQLSyncTool(SOURCE_DB_CONFIG, TARGET_DB_CONFIG)
        success = sync_tool.sync_all_data()
        
        if success:
            logger.info("数据库同步成功完成！")
            print("\n✅ 数据库同步成功完成！")
        else:
            logger.error("数据库同步失败！")
            print("\n❌ 数据库同步失败！请检查日志文件获取详细信息。")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断同步操作")
        print("\n⚠️ 同步操作被用户中断")
    except Exception as e:
        logger.error(f"同步过程中发生错误: {str(e)}")
        print(f"\n❌ 同步过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()