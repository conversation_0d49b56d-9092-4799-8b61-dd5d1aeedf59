#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库同步脚本
功能：从源MySQL数据库同步所有数据到目标MySQL数据库
作者：系统生成
创建时间：2024
"""

import pymysql
import logging
import sys
import argparse
import os
from typing import Dict, List, Tuple, Optional, Set
from datetime import datetime
from collections import defaultdict, deque

# 尝试导入配置文件
try:
    from sync_config import SOURCE_DB_CONFIG, TARGET_DB_CONFIG, SYNC_OPTIONS
except ImportError:
    SOURCE_DB_CONFIG = None
    TARGET_DB_CONFIG = None
    SYNC_OPTIONS = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/mysql_sync.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class MySQLSyncTool:
    """MySQL数据库同步工具类"""
    
    def __init__(self, source_config: Dict, target_config: Dict):
        """
        初始化同步工具
        
        Args:
            source_config: 源数据库配置
            target_config: 目标数据库配置
        """
        self.source_config = source_config
        self.target_config = target_config
        self.source_conn = None
        self.target_conn = None
        
    def connect_databases(self) -> bool:
        """
        连接源数据库和目标数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 连接源数据库
            self.source_conn = pymysql.connect(
                host=self.source_config['host'],
                port=self.source_config['port'],
                user=self.source_config['username'],
                password=self.source_config['password'],
                database=self.source_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接源数据库: {self.source_config['host']}:{self.source_config['port']}/{self.source_config['database']}")
            
            # 连接目标数据库
            self.target_conn = pymysql.connect(
                host=self.target_config['host'],
                port=self.target_config['port'],
                user=self.target_config['username'],
                password=self.target_config['password'],
                database=self.target_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接目标数据库: {self.target_config['host']}:{self.target_config['port']}/{self.target_config['database']}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def get_all_tables(self) -> List[str]:
        """
        获取源数据库中的所有表名
        
        Returns:
            List[str]: 表名列表
        """
        try:
            with self.source_conn.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = [row[0] for row in cursor.fetchall()]
                logger.info(f"源数据库共有 {len(tables)} 个表")
                return tables
        except Exception as e:
            logger.error(f"获取表列表失败: {str(e)}")
            return []
    
    def get_foreign_key_dependencies(self) -> Dict[str, Set[str]]:
        """
        获取表的外键依赖关系
        
        Returns:
            Dict[str, Set[str]]: 表名到其依赖表名集合的映射
        """
        dependencies = defaultdict(set)
        
        try:
            with self.source_conn.cursor() as cursor:
                # 查询所有外键约束
                query = """
                SELECT 
                    TABLE_NAME,
                    REFERENCED_TABLE_NAME
                FROM 
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE 
                    REFERENCED_TABLE_SCHEMA = %s
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                """
                
                cursor.execute(query, (self.source_config['database'],))
                results = cursor.fetchall()
                
                for table_name, referenced_table in results:
                    if referenced_table and referenced_table != table_name:
                        dependencies[table_name].add(referenced_table)
                        
                logger.info(f"分析外键依赖关系完成，共发现 {len(dependencies)} 个表有外键依赖")
                
                # 打印依赖关系
                for table, deps in dependencies.items():
                    logger.debug(f"表 {table} 依赖于: {', '.join(deps)}")
                    
                return dict(dependencies)
                
        except Exception as e:
            logger.error(f"获取外键依赖关系失败: {str(e)}")
            return {}
    
    def topological_sort(self, tables: List[str], dependencies: Dict[str, Set[str]]) -> List[str]:
        """
        对表进行拓扑排序，确保依赖表先于被依赖表创建
        
        Args:
            tables: 所有表名列表
            dependencies: 表的依赖关系
            
        Returns:
            List[str]: 按依赖顺序排序的表名列表
        """
        # 构建入度表
        in_degree = {table: 0 for table in tables}
        graph = defaultdict(list)
        
        # 构建图和计算入度
        for table in tables:
            if table in dependencies:
                for dep_table in dependencies[table]:
                    if dep_table in in_degree:  # 确保依赖的表存在
                        graph[dep_table].append(table)
                        in_degree[table] += 1
        
        # 使用队列进行拓扑排序
        queue = deque([table for table in tables if in_degree[table] == 0])
        sorted_tables = []
        
        while queue:
            current_table = queue.popleft()
            sorted_tables.append(current_table)
            
            # 更新相邻节点的入度
            for neighbor in graph[current_table]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        # 检查是否存在循环依赖
        if len(sorted_tables) != len(tables):
            remaining_tables = set(tables) - set(sorted_tables)
            logger.warning(f"检测到循环依赖，以下表可能存在循环引用: {', '.join(remaining_tables)}")
            # 将剩余的表添加到末尾
            sorted_tables.extend(remaining_tables)
        
        logger.info(f"表排序完成，同步顺序: {' -> '.join(sorted_tables)}")
        return sorted_tables
    
    def get_table_structure(self, table_name: str) -> str:
        """
        获取表的创建语句
        
        Args:
            table_name: 表名
            
        Returns:
            str: 创建表的SQL语句
        """
        try:
            with self.source_conn.cursor() as cursor:
                cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
                result = cursor.fetchone()
                return result[1] if result else ""
        except Exception as e:
            logger.error(f"获取表 {table_name} 结构失败: {str(e)}")
            return ""
    
    def clear_target_database(self) -> bool:
        """
        清空目标数据库的所有数据
        
        Returns:
            bool: 清空是否成功
        """
        try:
            with self.target_conn.cursor() as cursor:
                # 禁用外键检查
                cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
                
                # 获取目标数据库的所有表
                cursor.execute("SHOW TABLES")
                tables = [row[0] for row in cursor.fetchall()]
                
                # 删除所有表
                for table in tables:
                    cursor.execute(f"DROP TABLE IF EXISTS `{table}`")
                    logger.info(f"删除表: {table}")
                
                # 重新启用外键检查
                cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
                
                self.target_conn.commit()
                logger.info(f"成功清空目标数据库，共删除 {len(tables)} 个表")
                return True
                
        except Exception as e:
            logger.error(f"清空目标数据库失败: {str(e)}")
            self.target_conn.rollback()
            return False
    
    def create_table_in_target(self, table_name: str, create_sql: str) -> bool:
        """
        在目标数据库中创建表
        
        Args:
            table_name: 表名
            create_sql: 创建表的SQL语句
            
        Returns:
            bool: 创建是否成功
        """
        try:
            with self.target_conn.cursor() as cursor:
                # 禁用外键检查以避免创建时的依赖问题
                cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
                cursor.execute(create_sql)
                cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
                self.target_conn.commit()
                logger.info(f"成功创建表: {table_name}")
                return True
        except Exception as e:
            logger.error(f"创建表 {table_name} 失败: {str(e)}")
            self.target_conn.rollback()
            return False
    
    def sync_table_data(self, table_name: str) -> bool:
        """
        同步单个表的数据
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 同步是否成功
        """
        try:
            # 获取源表数据
            with self.source_conn.cursor() as source_cursor:
                source_cursor.execute(f"SELECT * FROM `{table_name}`")
                rows = source_cursor.fetchall()
                
                if not rows:
                    logger.info(f"表 {table_name} 无数据")
                    return True
                
                # 获取列信息
                source_cursor.execute(f"DESCRIBE `{table_name}`")
                columns = [col[0] for col in source_cursor.fetchall()]
                
            # 插入数据到目标表
            with self.target_conn.cursor() as target_cursor:
                # 构建插入语句
                placeholders = ', '.join(['%s'] * len(columns))
                column_names = ', '.join([f'`{col}`' for col in columns])
                insert_sql = f"INSERT INTO `{table_name}` ({column_names}) VALUES ({placeholders})"
                
                # 批量插入数据
                target_cursor.executemany(insert_sql, rows)
                self.target_conn.commit()
                
                logger.info(f"成功同步表 {table_name}，共 {len(rows)} 条记录")
                return True
                
        except Exception as e:
            logger.error(f"同步表 {table_name} 数据失败: {str(e)}")
            self.target_conn.rollback()
            return False
    
    def sync_all_data(self) -> bool:
        """
        同步所有数据
        
        Returns:
            bool: 同步是否成功
        """
        try:
            logger.info("开始数据库同步...")
            start_time = datetime.now()
            
            # 1. 连接数据库
            if not self.connect_databases():
                return False
            
            # 2. 清空目标数据库
            logger.info("清空目标数据库...")
            if not self.clear_target_database():
                return False
            
            # 3. 获取所有表
            tables = self.get_all_tables()
            if not tables:
                logger.warning("源数据库中没有表")
                return True
            
            # 4. 分析外键依赖关系
            logger.info("分析表的外键依赖关系...")
            dependencies = self.get_foreign_key_dependencies()
            
            # 5. 对表进行拓扑排序
            logger.info("对表进行依赖排序...")
            sorted_tables = self.topological_sort(tables, dependencies)
            
            # 6. 按依赖顺序同步表结构和数据
            success_count = 0
            for table_name in sorted_tables:
                logger.info(f"正在同步表: {table_name}")
                
                # 获取表结构
                create_sql = self.get_table_structure(table_name)
                if not create_sql:
                    logger.error(f"无法获取表 {table_name} 的结构")
                    continue
                
                # 创建表
                if not self.create_table_in_target(table_name, create_sql):
                    logger.error(f"创建表 {table_name} 失败")
                    continue
                
                # 同步数据
                if self.sync_table_data(table_name):
                    success_count += 1
                else:
                    logger.error(f"同步表 {table_name} 数据失败")
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info(f"数据库同步完成！")
            logger.info(f"总表数: {len(tables)}, 成功同步: {success_count}, 失败: {len(tables) - success_count}")
            logger.info(f"耗时: {duration.total_seconds():.2f} 秒")
            
            return success_count == len(tables)
            
        except Exception as e:
            logger.error(f"数据库同步失败: {str(e)}")
            return False
        finally:
            self.close_connections()
    
    def close_connections(self):
        """
        关闭数据库连接
        """
        if self.source_conn:
            self.source_conn.close()
            logger.info("已关闭源数据库连接")
        
        if self.target_conn:
            self.target_conn.close()
            logger.info("已关闭目标数据库连接")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='MySQL数据库同步工具')
    
    # 如果有配置文件，则参数变为可选
    required = SOURCE_DB_CONFIG is None or TARGET_DB_CONFIG is None
    
    parser.add_argument('--source-host', required=required, help='源数据库主机')
    parser.add_argument('--source-port', type=int, default=3306, help='源数据库端口')
    parser.add_argument('--source-user', required=required, help='源数据库用户名')
    parser.add_argument('--source-password', required=required, help='源数据库密码')
    parser.add_argument('--source-database', required=required, help='源数据库名')
    
    parser.add_argument('--target-host', required=required, help='目标数据库主机')
    parser.add_argument('--target-port', type=int, default=3306, help='目标数据库端口')
    parser.add_argument('--target-user', required=required, help='目标数据库用户名')
    parser.add_argument('--target-password', required=required, help='目标数据库密码')
    parser.add_argument('--target-database', required=required, help='目标数据库名')
    
    parser.add_argument('--use-config', action='store_true', help='使用配置文件中的设置')
    
    args = parser.parse_args()
    
    # 优先使用配置文件，如果没有配置文件或指定了命令行参数则使用命令行参数
    if SOURCE_DB_CONFIG and TARGET_DB_CONFIG and (args.use_config or not any([args.source_host, args.target_host])):
        logger.info("使用配置文件中的数据库配置")
        source_config = SOURCE_DB_CONFIG.copy()
        target_config = TARGET_DB_CONFIG.copy()
    else:
        logger.info("使用命令行参数配置")
        # 源数据库配置
        source_config = {
            'host': args.source_host,
            'port': args.source_port,
            'username': args.source_user,
            'password': args.source_password,
            'database': args.source_database
        }
        
        # 目标数据库配置
        target_config = {
            'host': args.target_host,
            'port': args.target_port,
            'username': args.target_user,
            'password': args.target_password,
            'database': args.target_database
        }
    
    # 创建同步工具并执行同步
    sync_tool = MySQLSyncTool(source_config, target_config)
    success = sync_tool.sync_all_data()
    
    if success:
        logger.info("数据库同步成功完成！")
        sys.exit(0)
    else:
        logger.error("数据库同步失败！")
        sys.exit(1)

if __name__ == '__main__':
    main()