#!/usr/bin/env python3
"""
添加业务类型字段到项目报价表的迁移脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from config.env import DataBaseConfig


async def migrate_business_type():
    """
    执行业务类型字段迁移
    """
    print("开始执行业务类型字段迁移...")

    try:
        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"

        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=True)

        # 迁移SQL语句
        migration_sql = """
        -- 检查字段是否已存在
        SELECT COUNT(*) as field_exists
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'project_quotation'
        AND COLUMN_NAME = 'business_type';
        """

        add_field_sql = """
        -- 添加 business_type 字段
        ALTER TABLE project_quotation
        ADD COLUMN business_type VARCHAR(50) DEFAULT 'sampling' COMMENT '业务类型：sampling-一般采样，sample-送样';
        """

        update_existing_sql = """
        -- 更新现有记录的默认值
        UPDATE project_quotation
        SET business_type = 'sampling'
        WHERE business_type IS NULL;
        """

        add_index_sql = """
        -- 添加索引以提高查询性能
        CREATE INDEX idx_project_quotation_business_type ON project_quotation(business_type);
        """

        verify_sql = """
        -- 验证字段添加成功
        SELECT COUNT(*) as total_records,
               COUNT(CASE WHEN business_type IS NOT NULL THEN 1 END) as records_with_business_type
        FROM project_quotation;
        """

        async with engine.begin() as conn:
            # 检查字段是否已存在
            result = await conn.execute(text(migration_sql))
            field_exists = result.fetchone()[0]

            if field_exists > 0:
                print("✅ business_type 字段已存在，跳过迁移")
                return True

            print("📝 添加 business_type 字段...")
            await conn.execute(text(add_field_sql))
            print("✅ business_type 字段添加成功")

            print("📝 更新现有记录...")
            await conn.execute(text(update_existing_sql))
            print("✅ 现有记录更新成功")

            print("📝 添加索引...")
            try:
                await conn.execute(text(add_index_sql))
                print("✅ 索引添加成功")
            except Exception as e:
                if "Duplicate key name" in str(e):
                    print("⚠️  索引已存在，跳过")
                else:
                    print(f"⚠️  索引添加失败: {e}")

            print("📝 验证迁移结果...")
            result = await conn.execute(text(verify_sql))
            row = result.fetchone()
            total_records = row[0]
            records_with_business_type = row[1]

            print(f"✅ 迁移完成！")
            print(f"   总记录数: {total_records}")
            print(f"   有业务类型的记录数: {records_with_business_type}")

            if total_records == records_with_business_type:
                print("✅ 所有记录都已正确设置业务类型")
            else:
                print("⚠️  部分记录的业务类型未设置")

        await engine.dispose()
        return True

    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def rollback_business_type():
    """
    回滚业务类型字段迁移
    """
    print("开始回滚业务类型字段迁移...")

    try:
        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"

        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=True)

        rollback_sql = """
        -- 删除索引
        DROP INDEX IF EXISTS idx_project_quotation_business_type ON project_quotation;

        -- 删除字段
        ALTER TABLE project_quotation DROP COLUMN IF EXISTS business_type;
        """

        async with engine.begin() as conn:
            await conn.execute(text(rollback_sql))
            print("✅ 回滚完成")

        await engine.dispose()
        return True

    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """
    主函数
    """
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        success = await rollback_business_type()
    else:
        success = await migrate_business_type()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    print("项目报价表业务类型字段迁移工具")
    print("用法:")
    print("  python migrate_business_type.py        # 执行迁移")
    print("  python migrate_business_type.py rollback # 回滚迁移")
    print()

    asyncio.run(main())
