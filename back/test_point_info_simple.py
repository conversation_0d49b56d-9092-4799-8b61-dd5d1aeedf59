#!/usr/bin/env python3
"""
简单的点位信息功能测试脚本
"""

import asyncio
import sys
import os
from decimal import Decimal
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from module_sampling.dto.sampling_point_info_dto import (
    SamplingPointInfoCreateDTO,
    SamplingPointInfoUpdateDTO,
    SamplingPointInfoDTO
)


def test_dto_creation():
    """测试DTO创建"""
    print("测试DTO创建...")
    
    # 测试创建DTO
    create_dto = SamplingPointInfoCreateDTO(
        sampling_task_group_id=1,
        point_name="测试点位",
        point_photo_url="http://example.com/point.jpg",
        point_photo_name="point.jpg",
        longitude=Decimal("116.397128"),
        latitude=Decimal("39.916527"),
        altitude=Decimal("50.5"),
        weather_condition="晴天",
        temperature=Decimal("25.5"),
        humidity=Decimal("60.0"),
        remarks="测试备注"
    )
    
    assert create_dto.sampling_task_group_id == 1
    assert create_dto.point_name == "测试点位"
    assert create_dto.longitude == Decimal("116.397128")
    print("✓ 创建DTO测试通过")
    
    # 测试更新DTO
    update_dto = SamplingPointInfoUpdateDTO(
        point_name="更新后的点位",
        temperature=Decimal("30.0"),
        remarks="更新后的备注"
    )
    
    assert update_dto.point_name == "更新后的点位"
    assert update_dto.temperature == Decimal("30.0")
    print("✓ 更新DTO测试通过")
    
    # 测试响应DTO
    response_dto = SamplingPointInfoDTO(
        id=1,
        sampling_task_group_id=1,
        point_name="响应测试点位",
        longitude=Decimal("116.397128"),
        latitude=Decimal("39.916527"),
        create_time=datetime.now()
    )
    
    assert response_dto.id == 1
    assert response_dto.point_name == "响应测试点位"
    print("✓ 响应DTO测试通过")


def test_data_model():
    """测试数据模型"""
    print("\n测试数据模型...")
    
    try:
        from module_sampling.entity.do.sampling_point_info_do import SamplingPointInfo
        
        # 创建数据模型实例
        point_info = SamplingPointInfo(
            sampling_task_group_id=1,
            point_name="模型测试点位",
            longitude=Decimal("116.397128"),
            latitude=Decimal("39.916527"),
            altitude=Decimal("50.5"),
            weather_condition="多云",
            temperature=Decimal("22.0"),
            create_by=1
        )
        
        assert point_info.sampling_task_group_id == 1
        assert point_info.point_name == "模型测试点位"
        print("✓ 数据模型创建测试通过")
        
        # 测试to_dict方法
        point_dict = point_info.to_dict()
        assert isinstance(point_dict, dict)
        assert point_dict['point_name'] == "模型测试点位"
        assert point_dict['longitude'] == 116.397128
        print("✓ 数据模型to_dict测试通过")
        
    except ImportError as e:
        print(f"✗ 数据模型导入失败: {e}")
        return False
    
    return True


def test_controller_import():
    """测试控制器导入"""
    print("\n测试控制器导入...")
    
    try:
        from module_sampling.controller.sampling_point_info_controller import router
        
        # 检查路由器是否正确创建
        assert router is not None
        assert hasattr(router, 'prefix')
        assert router.prefix == "/sampling/point-info"
        print("✓ 控制器导入测试通过")
        
        return True
        
    except ImportError as e:
        print(f"✗ 控制器导入失败: {e}")
        return False


def test_service_import():
    """测试服务层导入"""
    print("\n测试服务层导入...")
    
    try:
        from module_sampling.service.sampling_point_info_service import SamplingPointInfoService
        
        # 检查服务类是否正确定义
        assert SamplingPointInfoService is not None
        assert hasattr(SamplingPointInfoService, 'create_point_info')
        assert hasattr(SamplingPointInfoService, 'get_point_info_by_id')
        assert hasattr(SamplingPointInfoService, 'get_point_info_by_group_id')
        assert hasattr(SamplingPointInfoService, 'update_point_info')
        assert hasattr(SamplingPointInfoService, 'delete_point_info')
        print("✓ 服务层导入测试通过")
        
        return True
        
    except ImportError as e:
        print(f"✗ 服务层导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始点位信息功能测试...\n")
    
    tests = [
        test_dto_creation,
        test_data_model,
        test_controller_import,
        test_service_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            result = test()
            if result is not False:
                passed += 1
        except Exception as e:
            print(f"✗ 测试失败: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！点位信息功能实现正确。")
        return True
    else:
        print("❌ 部分测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
