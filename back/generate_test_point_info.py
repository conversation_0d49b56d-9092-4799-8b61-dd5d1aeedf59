#!/usr/bin/env python3
"""
为任务编号25080007生成测试点位信息
"""

import asyncio
import sys
import os
from decimal import Decimal
from datetime import datetime
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.get_db import get_db
from module_sampling.entity.do.sampling_point_info_do import SamplingPointInfo
from sqlalchemy import select


async def check_and_generate_point_info():
    """查询任务并生成点位信息"""
    async for db in get_db():
        try:
            # 查询任务编号为25080007的任务分组信息
            from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
            from module_sampling.entity.do.sampling_task_do import SamplingTask

            # 先查询任务
            task_query = select(SamplingTask).where(SamplingTask.task_code == '25080007')
            task_result = await db.execute(task_query)
            task = task_result.scalar_one_or_none()

            if not task:
                print("未找到任务编号为25080007的任务")
                return

            print(f'找到任务: ID={task.id}, 名称={task.task_name}, 编号={task.task_code}')

            # 查询该任务的分组信息
            query = select(SamplingTaskGroup).where(SamplingTaskGroup.sampling_task_id == task.id)
            result = await db.execute(query)
            task_groups = result.scalars().all()
            
            print(f'找到 {len(task_groups)} 个任务分组:')
            for group in task_groups:
                print(f'分组ID: {group.id}, 任务ID: {group.sampling_task_id}, 分组名称: {group.group_name}')
            
            if not task_groups:
                print("未找到任务编号为25080007的任务分组")
                return
            
            # 为每个分组生成点位信息
            for i, group in enumerate(task_groups):
                # 检查是否已存在点位信息
                existing_query = select(SamplingPointInfo).where(
                    SamplingPointInfo.sampling_task_group_id == group.id
                )
                existing_result = await db.execute(existing_query)
                existing_point = existing_result.scalar_one_or_none()
                
                if existing_point:
                    print(f'分组ID {group.id} 已存在点位信息，跳过')
                    continue
                
                # 生成随机的经纬度（北京地区）
                longitude = Decimal(str(round(116.3 + random.uniform(-0.1, 0.1), 6)))
                latitude = Decimal(str(round(39.9 + random.uniform(-0.1, 0.1), 6)))
                
                # 生成随机的环境数据
                temperature = Decimal(str(round(random.uniform(15, 30), 1)))
                humidity = Decimal(str(round(random.uniform(40, 80), 1)))
                wind_speed = Decimal(str(round(random.uniform(0, 5), 1)))
                altitude = Decimal(str(round(random.uniform(30, 100), 1)))
                
                # 随机选择天气和风向
                weather_conditions = ['晴天', '多云', '阴天', '小雨', '雾霾']
                wind_directions = ['东风', '南风', '西风', '北风', '东南风', '西南风', '东北风', '西北风']
                
                # 生成测试照片URL（模拟）
                base_url = "http://127.0.0.1:9099/profile/upload/2024/12/29"
                point_photo_name = f"point_{group.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}A{random.randint(100, 999)}.jpg"
                east_photo_name = f"east_{group.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}A{random.randint(100, 999)}.jpg"
                south_photo_name = f"south_{group.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}A{random.randint(100, 999)}.jpg"
                west_photo_name = f"west_{group.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}A{random.randint(100, 999)}.jpg"
                north_photo_name = f"north_{group.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}A{random.randint(100, 999)}.jpg"
                
                # 创建点位信息
                point_info = SamplingPointInfo(
                    sampling_task_group_id=group.id,
                    point_name=f"测试点位-{group.group_name or f'分组{i+1}'}",
                    
                    # 点位照片
                    point_photo_url=f"{base_url}/{point_photo_name}",
                    point_photo_name=point_photo_name,
                    
                    # 环境照片
                    east_photo_url=f"{base_url}/{east_photo_name}",
                    east_photo_name=east_photo_name,
                    south_photo_url=f"{base_url}/{south_photo_name}",
                    south_photo_name=south_photo_name,
                    west_photo_url=f"{base_url}/{west_photo_name}",
                    west_photo_name=west_photo_name,
                    north_photo_url=f"{base_url}/{north_photo_name}",
                    north_photo_name=north_photo_name,
                    
                    # 位置信息
                    longitude=longitude,
                    latitude=latitude,
                    coordinate_system='WGS84',
                    altitude=altitude,
                    
                    # 环境信息
                    sampling_time=datetime.now(),
                    weather_condition=random.choice(weather_conditions),
                    temperature=temperature,
                    humidity=humidity,
                    wind_speed=wind_speed,
                    wind_direction=random.choice(wind_directions),
                    
                    # 备注
                    remarks=f"这是为任务编号25080007的{group.group_name or f'分组{i+1}'}生成的测试点位信息。包含模拟的照片、位置和环境数据。",
                    
                    # 审计字段
                    create_by=1,  # 假设管理员用户ID为1
                    update_by=1
                )
                
                db.add(point_info)
                print(f'为分组ID {group.id} 生成点位信息: {point_info.point_name}')
                print(f'  经纬度: ({longitude}, {latitude})')
                print(f'  天气: {point_info.weather_condition}, 温度: {temperature}°C, 湿度: {humidity}%')
                print(f'  风速: {wind_speed}m/s, 风向: {point_info.wind_direction}')
                print(f'  照片数量: 5张 (1张点位照片 + 4张环境照片)')
                print()
            
            # 提交事务
            await db.commit()
            print("✅ 所有点位信息生成完成！")
            
        except Exception as e:
            await db.rollback()
            print(f'❌ 生成失败: {e}')
            import traceback
            traceback.print_exc()
        finally:
            await db.close()


async def create_mock_photos():
    """创建模拟照片文件"""
    import os
    from pathlib import Path
    
    # 创建上传目录
    upload_dir = Path("vf_admin/upload_path/upload/2024/12/29")
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建一个简单的测试图片内容（1x1像素的PNG）
    # 这是一个最小的PNG文件的二进制数据
    png_data = bytes([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,  # PNG signature
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,  # IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,  # 1x1 pixel
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,  # RGB, no compression
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41,  # IDAT chunk
        0x54, 0x08, 0xD7, 0x63, 0xF8, 0x0F, 0x00, 0x00,  # compressed data
        0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8E, 0x00,  # CRC
        0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,  # IEND chunk
        0x42, 0x60, 0x82
    ])
    
    # 生成一些测试照片文件
    photo_types = ['point', 'east', 'south', 'west', 'north']
    for i in range(1, 6):  # 假设有5个分组
        for photo_type in photo_types:
            filename = f"{photo_type}_{i}_{datetime.now().strftime('%Y%m%d%H%M%S')}A{random.randint(100, 999)}.jpg"
            filepath = upload_dir / filename
            with open(filepath, 'wb') as f:
                f.write(png_data)
    
    print(f"✅ 在 {upload_dir} 创建了测试照片文件")


if __name__ == "__main__":
    print("开始为任务编号25080007生成测试点位信息...\n")
    
    # 创建模拟照片
    asyncio.run(create_mock_photos())
    
    # 生成点位信息
    asyncio.run(check_and_generate_point_info())
