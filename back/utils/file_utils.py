"""
文件处理工具类
"""

import os
import time
import random
import string
import shutil
from typing import List, Dict, Any
from datetime import datetime
from pathlib import Path
from fastapi import UploadFile
import logging

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器"""

    def __init__(self, base_path: str = "attachedFiles"):
        """
        初始化文件管理器

        Args:
            base_path: 附件存储基础路径
        """
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)

    def generate_unique_filename(self, original_filename: str) -> str:
        """
        生成全局唯一的文件名
        规则：ZJQS_时间戳_随机4位数字

        Args:
            original_filename: 原始文件名

        Returns:
            str: 唯一文件名
        """
        # 获取文件扩展名
        file_ext = Path(original_filename).suffix

        # 生成时间戳
        timestamp = str(int(time.time()))

        # 生成随机4位数字
        random_digits = "".join(random.choices(string.digits, k=4))

        # 组合唯一文件名
        unique_filename = f"ZJQS_{timestamp}_{random_digits}{file_ext}"

        return unique_filename

    async def save_file(self, file: UploadFile, module: str = "equipment") -> Dict[str, Any]:
        """
        保存上传的文件

        Args:
            file: 上传的文件
            module: 模块名称，用于分类存储

        Returns:
            Dict: 文件信息
        """
        try:
            # 创建模块目录
            module_path = self.base_path / module
            module_path.mkdir(exist_ok=True)

            # 生成唯一文件名
            unique_filename = self.generate_unique_filename(file.filename)

            # 完整文件路径
            file_path = module_path / unique_filename

            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)

            # 获取文件大小
            file_size = file_path.stat().st_size

            # 返回文件信息
            file_info = {
                "fileName": file.filename,
                "uniqueId": unique_filename,
                "filePath": str(file_path.relative_to(self.base_path)),
                "fileSize": file_size,
                "uploadTime": datetime.now(),
            }

            logger.info(f"文件保存成功: {file_info}")
            return file_info

        except Exception as e:
            logger.error(f"文件保存失败: {str(e)}")
            raise Exception(f"文件保存失败: {str(e)}")

    async def save_multiple_files(self, files: List[UploadFile], module: str = "equipment") -> List[Dict[str, Any]]:
        """
        批量保存多个文件

        Args:
            files: 上传的文件列表
            module: 模块名称

        Returns:
            List[Dict]: 文件信息列表
        """
        file_infos = []

        for file in files:
            if file.filename:  # 确保文件名不为空
                file_info = await self.save_file(file, module)
                file_infos.append(file_info)

        return file_infos

    def delete_file(self, file_path: str) -> bool:
        """
        删除文件

        Args:
            file_path: 文件相对路径

        Returns:
            bool: 删除是否成功
        """
        try:
            full_path = self.get_file_path(file_path)
            if full_path.exists():
                full_path.unlink()
                logger.info(f"文件删除成功: {file_path}")
                return True
            else:
                logger.warning(f"文件不存在: {file_path}")
                return False
        except Exception as e:
            logger.error(f"文件删除失败: {str(e)}")
            return False

    def delete_multiple_files(self, file_paths: List[str]) -> Dict[str, int]:
        """
        批量删除文件

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict: 删除结果统计
        """
        success_count = 0
        failed_count = 0

        for file_path in file_paths:
            if self.delete_file(file_path):
                success_count += 1
            else:
                failed_count += 1

        return {"success_count": success_count, "failed_count": failed_count, "total_count": len(file_paths)}

    def get_file_path(self, relative_path: str) -> Path:
        """
        获取文件的完整路径

        Args:
            relative_path: 相对路径

        Returns:
            Path: 完整路径
        """
        return self.base_path / relative_path

    def file_exists(self, relative_path: str) -> bool:
        """
        检查文件是否存在

        Args:
            relative_path: 相对路径

        Returns:
            bool: 文件是否存在
        """
        return self.get_file_path(relative_path).exists()

    def get_file_info(self, relative_path: str) -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            relative_path: 相对路径

        Returns:
            Dict: 文件信息
        """
        file_path = self.get_file_path(relative_path)

        if not file_path.exists():
            return None

        stat = file_path.stat()

        return {
            "file_name": file_path.name,
            "file_path": relative_path,
            "file_size": stat.st_size,
            "create_time": datetime.fromtimestamp(stat.st_ctime),
            "modify_time": datetime.fromtimestamp(stat.st_mtime),
        }


# 全局文件管理器实例
file_manager = FileManager()
