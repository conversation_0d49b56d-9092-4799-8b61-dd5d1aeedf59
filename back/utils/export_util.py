import os
import tempfile
from typing import List, Dict, Any, Optional
from datetime import datetime

import pandas as pd
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import Pt
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
from fastapi import HTTPException
from fastapi.responses import FileResponse


class ExportUtil:
    """
    导出工具类，支持导出Excel、PDF和Word格式
    """
    
    @staticmethod
    async def export_excel(data: List[Dict[str, Any]], filename: str, headers: Optional[Dict[str, str]] = None) -> FileResponse:
        """
        导出Excel文件
        
        :param data: 要导出的数据列表
        :param filename: 文件名（不含扩展名）
        :param headers: 表头映射，键为数据字段名，值为表头显示名
        :return: FileResponse对象
        """
        try:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            file_path = os.path.join(temp_dir, f"{filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx")
            
            # 如果提供了表头映射，则重命名列
            if headers:
                df = pd.DataFrame(data)
                # 只保留headers中指定的列
                df = df[[col for col in headers.keys() if col in df.columns]]
                df = df.rename(columns=headers)
            else:
                df = pd.DataFrame(data)
            
            # 导出到Excel
            df.to_excel(file_path, index=False)
            
            # 返回文件
            return FileResponse(
                path=file_path,
                filename=os.path.basename(file_path),
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"导出Excel失败: {str(e)}")
    
    @staticmethod
    async def export_pdf(data: List[Dict[str, Any]], filename: str, headers: Optional[Dict[str, str]] = None, title: Optional[str] = None) -> FileResponse:
        """
        导出PDF文件
        
        :param data: 要导出的数据列表
        :param filename: 文件名（不含扩展名）
        :param headers: 表头映射，键为数据字段名，值为表头显示名
        :param title: PDF标题
        :return: FileResponse对象
        """
        try:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            file_path = os.path.join(temp_dir, f"{filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf")
            
            # 创建PDF文档
            doc = SimpleDocTemplate(file_path, pagesize=letter)
            elements = []
            
            # 添加标题
            if title:
                styles = getSampleStyleSheet()
                title_style = styles['Title']
                elements.append(Paragraph(title, title_style))
            
            # 准备表格数据
            if headers:
                # 只使用headers中指定的列
                header_keys = list(headers.keys())
                table_data = [list(headers.values())]  # 表头行
                for item in data:
                    row = [item.get(key, '') for key in header_keys]
                    table_data.append(row)
            else:
                if data:
                    table_data = [list(data[0].keys())]  # 表头行
                    for item in data:
                        row = list(item.values())
                        table_data.append(row)
                else:
                    table_data = []
            
            # 创建表格
            if table_data:
                table = Table(table_data)
                
                # 设置表格样式
                style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ])
                table.setStyle(style)
                elements.append(table)
            
            # 构建PDF
            doc.build(elements)
            
            # 返回文件
            return FileResponse(
                path=file_path,
                filename=os.path.basename(file_path),
                media_type="application/pdf"
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"导出PDF失败: {str(e)}")
    
    @staticmethod
    async def export_word(data: List[Dict[str, Any]], filename: str, headers: Optional[Dict[str, str]] = None, title: Optional[str] = None) -> FileResponse:
        """
        导出Word文件
        
        :param data: 要导出的数据列表
        :param filename: 文件名（不含扩展名）
        :param headers: 表头映射，键为数据字段名，值为表头显示名
        :param title: Word文档标题
        :return: FileResponse对象
        """
        try:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            file_path = os.path.join(temp_dir, f"{filename}_{datetime.now().strftime('%Y%m%d%H%M%S')}.docx")
            
            # 创建Word文档
            doc = Document()
            
            # 添加标题
            if title:
                doc.add_heading(title, level=1)
                heading = doc.paragraphs[-1]
                heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 准备表格数据
            if headers:
                # 只使用headers中指定的列
                header_keys = list(headers.keys())
                header_values = list(headers.values())
                
                # 创建表格
                table = doc.add_table(rows=1, cols=len(header_keys))
                table.style = 'Table Grid'
                
                # 添加表头
                header_cells = table.rows[0].cells
                for i, value in enumerate(header_values):
                    header_cells[i].text = value
                    # 设置表头样式
                    for paragraph in header_cells[i].paragraphs:
                        for run in paragraph.runs:
                            run.font.bold = True
                            run.font.size = Pt(11)
                
                # 添加数据行
                for item in data:
                    row_cells = table.add_row().cells
                    for i, key in enumerate(header_keys):
                        row_cells[i].text = str(item.get(key, ''))
            else:
                if data:
                    # 获取所有列名
                    columns = list(data[0].keys())
                    
                    # 创建表格
                    table = doc.add_table(rows=1, cols=len(columns))
                    table.style = 'Table Grid'
                    
                    # 添加表头
                    header_cells = table.rows[0].cells
                    for i, column in enumerate(columns):
                        header_cells[i].text = column
                        # 设置表头样式
                        for paragraph in header_cells[i].paragraphs:
                            for run in paragraph.runs:
                                run.font.bold = True
                                run.font.size = Pt(11)
                    
                    # 添加数据行
                    for item in data:
                        row_cells = table.add_row().cells
                        for i, column in enumerate(columns):
                            row_cells[i].text = str(item.get(column, ''))
            
            # 保存文档
            doc.save(file_path)
            
            # 返回文件
            return FileResponse(
                path=file_path,
                filename=os.path.basename(file_path),
                media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"导出Word失败: {str(e)}")
