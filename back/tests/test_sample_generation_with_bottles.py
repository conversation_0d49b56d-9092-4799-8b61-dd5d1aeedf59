#!/usr/bin/env python3
"""
测试样品生成时自动生成瓶组的功能
"""

import asyncio
import requests
import json

# 测试配置
BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def test_sample_generation_with_bottles():
    """测试样品生成时自动生成瓶组"""
    
    print("=" * 60)
    print("测试样品生成时自动生成瓶组功能")
    print("=" * 60)
    
    # 1. 检查服务器状态
    print("检查服务器状态...")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
        else:
            print("✗ 服务器状态异常")
            return
    except Exception as e:
        print(f"✗ 无法连接到服务器: {e}")
        return
    
    # 2. 获取任务15的分组信息
    print("\n获取任务15的分组信息...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/task/15",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            groups = data.get('data', [])
            print(f"✓ 获取到 {len(groups)} 个分组")
            
            if not groups:
                print("✗ 没有找到分组，无法继续测试")
                return
                
            # 选择第一个分组进行测试
            test_group = groups[0]
            group_id = test_group['id']
            print(f"  - 测试分组ID: {group_id}")
            print(f"  - 分组名称: {test_group.get('groupName', 'N/A')}")
            print(f"  - 当前状态: {test_group.get('status', 'N/A')}")
            
        else:
            print(f"✗ 获取分组信息失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return
            
    except Exception as e:
        print(f"✗ 获取分组信息异常: {e}")
        return
    
    # 3. 生成样品记录（这会自动触发瓶组生成）
    print(f"\n为分组 {group_id} 生成样品记录...")
    try:
        response = requests.post(
            f"{BASE_URL}/sampling/sample-records/generate/group/{group_id}",
            headers=HEADERS
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ 样品记录生成成功")
            
            sample_records = data.get('data', [])
            print(f"  - 生成样品记录数: {len(sample_records)}")
            
            # 显示样品记录信息
            for i, record in enumerate(sample_records[:3], 1):  # 只显示前3个
                print(f"    样品{i}: ID={record.get('id')}, 编号={record.get('sampleNumber')}")
                
            if len(sample_records) > 3:
                print(f"    ... 还有 {len(sample_records) - 3} 个样品记录")
                
        else:
            print(f"✗ 样品记录生成失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return
            
    except Exception as e:
        print(f"✗ 生成样品记录异常: {e}")
        return
    
    # 4. 检查是否自动生成了瓶组
    print(f"\n检查任务15是否自动生成了瓶组...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/15",
            headers=HEADERS
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ 获取瓶组列表成功")
            
            bottle_groups = data.get('data', [])
            print(f"  - 瓶组总数: {len(bottle_groups)}")
            
            if bottle_groups:
                print("  - 瓶组列表:")
                for i, group in enumerate(bottle_groups[:5], 1):  # 只显示前5个
                    bottle_type = "匹配瓶组" if group.get('bottleMaintenanceId') else "默认瓶组"
                    print(f"    瓶组{i}: {group.get('bottleGroupCode')} - {bottle_type} - {group.get('sampleCount')}个样品")
                    print(f"           检测方法: {group.get('detectionMethod', 'N/A')[:50]}...")
                    
                if len(bottle_groups) > 5:
                    print(f"    ... 还有 {len(bottle_groups) - 5} 个瓶组")
                    
                # 统计瓶组类型
                default_count = sum(1 for g in bottle_groups if not g.get('bottleMaintenanceId'))
                matched_count = len(bottle_groups) - default_count
                print(f"\n  - 瓶组统计:")
                print(f"    默认瓶组: {default_count}")
                print(f"    匹配瓶组: {matched_count}")
                
            else:
                print("  - 暂无瓶组记录")
                
        else:
            print(f"✗ 获取瓶组列表失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"✗ 获取瓶组列表异常: {e}")
    
    print("\n" + "=" * 60)
    if len(bottle_groups) > 0:
        print("✅ 测试成功！样品生成时自动生成了瓶组")
    else:
        print("❌ 测试失败！样品生成时没有自动生成瓶组")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_sample_generation_with_bottles())
