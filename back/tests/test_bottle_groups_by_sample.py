#!/usr/bin/env python3
"""
测试获取样品关联瓶组的API
"""

import asyncio
import requests
import json

# 测试配置
BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def test_bottle_groups_by_sample():
    """测试获取样品关联瓶组的API"""
    
    print("=" * 60)
    print("测试获取样品关联瓶组的API")
    print("=" * 60)
    
    # 1. 获取一个有瓶组的样品ID
    print("查找有瓶组的样品...")
    try:
        # 先获取任务13的瓶组列表
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/13",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            bottle_groups = data.get('data', [])
            print(f"✓ 任务13有 {len(bottle_groups)} 个瓶组")
            
            if not bottle_groups:
                print("✗ 任务13没有瓶组，无法测试")
                return
                
            # 获取第一个瓶组的样品信息
            first_bottle = bottle_groups[0]
            bottle_id = first_bottle['id']
            bottle_code = first_bottle['bottleGroupCode']
            print(f"  - 选择瓶组: {bottle_code} (ID={bottle_id})")
            
        else:
            print(f"✗ 获取瓶组列表失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"✗ 获取瓶组列表异常: {e}")
        return
    
    # 2. 获取瓶组关联的样品
    print(f"\n获取瓶组 {bottle_code} 关联的样品...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/{bottle_id}/samples",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            samples = data.get('data', [])
            print(f"✓ 瓶组 {bottle_code} 关联了 {len(samples)} 个样品")
            
            if not samples:
                print("✗ 瓶组没有关联样品，无法测试")
                return
                
            # 选择第一个样品进行测试
            test_sample = samples[0]
            sample_id = test_sample['id']
            sample_number = test_sample['sampleNumber']
            print(f"  - 选择样品: {sample_number} (ID={sample_id})")
            
        else:
            print(f"✗ 获取瓶组样品失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"✗ 获取瓶组样品异常: {e}")
        return
    
    # 3. 测试获取样品关联的瓶组
    print(f"\n测试获取样品 {sample_number} 关联的瓶组...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
            headers=HEADERS
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ API调用成功")
            
            related_bottles = data.get('data', [])
            print(f"  - 样品 {sample_number} 关联了 {len(related_bottles)} 个瓶组")
            
            if related_bottles:
                print("  - 关联的瓶组列表:")
                for i, bottle in enumerate(related_bottles[:5], 1):
                    bottle_type = "匹配瓶组" if bottle.get('bottleMaintenanceId') else "默认瓶组"
                    print(f"    瓶组{i}: {bottle.get('bottleGroupCode')} - {bottle_type}")
                    print(f"           检测方法: {bottle.get('detectionMethod', 'N/A')[:50]}...")
                    print(f"           瓶组状态: {bottle.get('status')}")
                    
                if len(related_bottles) > 5:
                    print(f"    ... 还有 {len(related_bottles) - 5} 个瓶组")
                    
            else:
                print("  - 样品没有关联瓶组")
                
        else:
            print(f"✗ API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"✗ API调用异常: {e}")
    
    print("\n" + "=" * 60)
    if response.status_code == 200 and len(related_bottles) > 0:
        print("✅ 测试成功！样品关联瓶组API正常工作")
    else:
        print("❌ 测试失败！样品关联瓶组API有问题")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_bottle_groups_by_sample())
