#!/usr/bin/env python3
"""
测试最终的瓶组集成功能
"""

import asyncio
import requests
import json

# 测试配置
BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def test_final_bottle_integration():
    """测试最终的瓶组集成功能"""
    
    print("=" * 80)
    print("🧪 测试最终的瓶组集成功能")
    print("=" * 80)
    
    # 1. 获取有样品记录的分组
    print("1. 🔍 查找有样品记录的分组...")
    try:
        # 使用分组19（已知有样品记录）
        group_id = 19
        response = requests.get(
            f"{BASE_URL}/sampling/sample-records/group/{group_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            sample_records = data.get('data', [])
            print(f"✅ 分组 {group_id} 有 {len(sample_records)} 个样品记录")
            
            if not sample_records:
                print("❌ 没有找到样品记录，无法测试")
                return
                
        else:
            print(f"❌ 获取样品记录失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 获取样品记录异常: {e}")
        return
    
    # 2. 测试每个样品的瓶组关联
    print(f"\n2. 🔗 测试样品瓶组关联功能...")
    sample_bottle_data = []
    
    for i, sample in enumerate(sample_records[:3], 1):  # 只测试前3个样品
        sample_id = sample['id']
        sample_number = sample['sampleNumber']
        
        print(f"   📋 测试样品 {i}: {sample_number} (ID={sample_id})")
        
        try:
            response = requests.get(
                f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
                headers=HEADERS
            )
            
            if response.status_code == 200:
                data = response.json()
                bottle_groups = data.get('data', [])
                
                sample_info = {
                    'sample': sample,
                    'bottle_groups': bottle_groups,
                    'bottle_count': len(bottle_groups)
                }
                sample_bottle_data.append(sample_info)
                
                print(f"      ✅ 关联了 {len(bottle_groups)} 个瓶组")
                
                # 显示瓶组信息
                for j, bottle in enumerate(bottle_groups[:2], 1):
                    bottle_type = "匹配瓶组" if bottle.get('bottleMaintenanceId') else "默认瓶组"
                    status_map = {0: '待采集', 1: '已采集', 2: '已送检'}
                    status = status_map.get(bottle.get('status'), '未知')
                    print(f"         🧪 瓶组{j}: {bottle.get('bottleGroupCode')} ({bottle_type}, {status})")
                    
                if len(bottle_groups) > 2:
                    print(f"         ... 还有 {len(bottle_groups) - 2} 个瓶组")
                    
            else:
                print(f"      ❌ 获取瓶组失败，状态码: {response.status_code}")
                sample_bottle_data.append({
                    'sample': sample,
                    'bottle_groups': [],
                    'bottle_count': 0
                })
                
        except Exception as e:
            print(f"      ❌ 获取瓶组异常: {e}")
            sample_bottle_data.append({
                'sample': sample,
                'bottle_groups': [],
                'bottle_count': 0
            })
    
    # 3. 验证前端数据结构
    print(f"\n3. 📊 验证前端数据结构...")
    
    # 模拟前端数据结构
    frontend_samples = []
    for item in sample_bottle_data:
        sample_with_bottles = {
            **item['sample'],
            'bottleGroups': item['bottle_groups']
        }
        frontend_samples.append(sample_with_bottles)
    
    print(f"   ✅ 构建了 {len(frontend_samples)} 个样品记录的前端数据")
    print(f"   📈 数据统计:")
    
    total_bottles = sum(item['bottle_count'] for item in sample_bottle_data)
    samples_with_bottles = sum(1 for item in sample_bottle_data if item['bottle_count'] > 0)
    
    print(f"      - 样品总数: {len(sample_bottle_data)}")
    print(f"      - 瓶组总数: {total_bottles}")
    print(f"      - 有瓶组的样品数: {samples_with_bottles}")
    print(f"      - 平均每样品瓶组数: {total_bottles / len(sample_bottle_data) if sample_bottle_data else 0:.1f}")
    
    # 4. 测试瓶组操作功能
    print(f"\n4. ⚙️ 测试瓶组操作功能...")
    
    if total_bottles > 0:
        # 找一个待采集的瓶组进行测试
        test_bottle = None
        for item in sample_bottle_data:
            for bottle in item['bottle_groups']:
                if bottle.get('status') == 0:  # 待采集状态
                    test_bottle = bottle
                    break
            if test_bottle:
                break
        
        if test_bottle:
            bottle_id = test_bottle['id']
            bottle_code = test_bottle['bottleGroupCode']
            print(f"   🧪 选择瓶组进行操作测试: {bottle_code}")
            
            # 测试瓶组采集
            try:
                response = requests.put(
                    f"{BASE_URL}/sampling/bottle-groups/{bottle_id}/status",
                    headers=HEADERS,
                    json=1  # 设置为已采集状态
                )
                
                if response.status_code == 200:
                    print(f"      ✅ 瓶组采集操作成功")
                    
                    # 恢复原状态
                    requests.put(
                        f"{BASE_URL}/sampling/bottle-groups/{bottle_id}/status",
                        headers=HEADERS,
                        json=0  # 恢复为待采集状态
                    )
                    print(f"      🔄 已恢复瓶组原状态")
                    
                else:
                    print(f"      ❌ 瓶组采集操作失败，状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ 瓶组操作异常: {e}")
        else:
            print(f"   ⚠️ 没有找到待采集的瓶组，跳过操作测试")
    else:
        print(f"   ⚠️ 没有瓶组数据，跳过操作测试")
    
    # 5. 总结测试结果
    print(f"\n" + "=" * 80)
    print("📋 测试结果总结")
    print("=" * 80)
    
    success_criteria = [
        (len(sample_records) > 0, "✅ 样品记录获取正常"),
        (total_bottles > 0, "✅ 瓶组关联查询正常"),
        (samples_with_bottles > 0, "✅ 样品瓶组关联正常"),
        (len(frontend_samples) > 0, "✅ 前端数据结构正确")
    ]
    
    passed_tests = sum(1 for passed, _ in success_criteria if passed)
    total_tests = len(success_criteria)
    
    print(f"🎯 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    print()
    
    for passed, message in success_criteria:
        print(f"   {message if passed else message.replace('✅', '❌')}")
    
    print()
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！前端瓶组集成功能完全正常")
        print()
        print("📱 前端使用指南:")
        print("   1. 登录系统 → 采样执行页面")
        print("   2. 点击任务的'样品管理'按钮")
        print("   3. 在样品记录表格中点击'点击展开瓶组'")
        print("   4. 查看样品关联的瓶组信息（卡片式布局）")
        print("   5. 直接在瓶组卡片中进行采集、送检操作")
        print()
        print("✨ 新功能特点:")
        print("   • 瓶组信息直接关联在样品记录下")
        print("   • 卡片式布局，信息展示更清晰")
        print("   • 支持展开/收起，节省界面空间")
        print("   • 可直接在瓶组卡片中操作")
    else:
        print("❌ 部分测试失败，请检查相关功能")
    
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(test_final_bottle_integration())
