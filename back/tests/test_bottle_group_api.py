"""
测试瓶组API接口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import httpx
import json


async def test_bottle_group_api():
    """测试瓶组API接口"""
    print("测试瓶组API接口...")
    
    base_url = "http://127.0.0.1:9099"
    headers = {
        "Authorization": "Bearer test_token",
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            # 测试生成瓶组接口
            task_id = 15
            print(f"测试为任务 {task_id} 生成瓶组...")
            
            response = await client.post(
                f"{base_url}/sampling/bottle-groups/generate/{task_id}",
                headers=headers,
                timeout=30.0
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print("✓ 瓶组生成API调用成功")
                
                if result.get("code") == 200:
                    data = result.get("data", {})
                    print(f"  - 生成瓶组总数: {data.get('totalGroups', 0)}")
                    print(f"  - 默认瓶组数: {data.get('defaultGroups', 0)}")
                    print(f"  - 匹配瓶组数: {data.get('matchedGroups', 0)}")
                    
                    bottle_groups = data.get('bottleGroups', [])
                    for i, group in enumerate(bottle_groups[:3]):  # 只显示前3个
                        print(f"  - 瓶组{i+1}: {group.get('bottleGroupCode')} ({group.get('sampleCount')}个样品)")
                    
                    print("✓ 瓶组生成成功")
                    return True
                else:
                    print(f"✗ 瓶组生成失败: {result.get('msg', '未知错误')}")
                    return False
            else:
                print(f"✗ API调用失败，状态码: {response.status_code}")
                return False
                
        except httpx.ConnectError:
            print("✗ 无法连接到服务器，请确保后端服务已启动")
            return False
        except Exception as e:
            print(f"✗ API测试失败: {e}")
            return False


async def test_get_bottle_groups():
    """测试获取瓶组列表接口"""
    print("\n测试获取瓶组列表接口...")
    
    base_url = "http://127.0.0.1:9099"
    headers = {
        "Authorization": "Bearer test_token",
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            task_id = 15
            response = await client.get(
                f"{base_url}/sampling/bottle-groups/task/{task_id}",
                headers=headers,
                timeout=30.0
            )
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✓ 获取瓶组列表API调用成功")
                
                if result.get("code") == 200:
                    bottle_groups = result.get("data", [])
                    print(f"  - 瓶组总数: {len(bottle_groups)}")
                    
                    for i, group in enumerate(bottle_groups[:3]):  # 只显示前3个
                        print(f"  - 瓶组{i+1}: {group.get('bottleGroupCode')} - {group.get('bottleType')} - {group.get('sampleCount')}个样品")
                    
                    return True
                else:
                    print(f"✗ 获取瓶组列表失败: {result.get('msg', '未知错误')}")
                    return False
            else:
                print(f"✗ API调用失败，状态码: {response.status_code}")
                return False
                
        except httpx.ConnectError:
            print("✗ 无法连接到服务器，请确保后端服务已启动")
            return False
        except Exception as e:
            print(f"✗ API测试失败: {e}")
            return False


def test_server_status():
    """测试服务器状态"""
    print("测试服务器状态...")
    
    try:
        import requests
        
        response = requests.get("http://127.0.0.1:9099/docs", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常，Swagger文档可访问")
            return True
        else:
            print(f"✗ 服务器响应异常，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectError:
        print("✗ 无法连接到服务器")
        print("请确保后端服务已启动：")
        print("  cd /home/<USER>/workspaces/lims2/back")
        print("  source venv/bin/activate")
        print("  python run.py")
        return False
    except Exception as e:
        print(f"✗ 服务器状态检查失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("=" * 60)
    print("瓶组API接口测试")
    print("=" * 60)
    
    # 检查服务器状态
    if not test_server_status():
        print("\n请先启动后端服务，然后重新运行此测试")
        return False
    
    all_passed = True
    
    # 测试生成瓶组API
    if not await test_bottle_group_api():
        all_passed = False
    
    # 测试获取瓶组列表API
    if not await test_get_bottle_groups():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ 所有API测试通过！瓶组功能正常工作")
    else:
        print("❌ 部分API测试失败")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
