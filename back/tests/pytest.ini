[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 异步测试支持
asyncio_mode = auto

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 标记定义
markers =
    asyncio: 异步测试
    integration: 集成测试
    unit: 单元测试
    api: API测试
    dao: DAO层测试
    service: 服务层测试
    slow: 慢速测试

# 最小版本要求
minversion = 6.0

# 测试路径
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
