"""
测试瓶组SQL查询
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import select
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup


def test_sql_query():
    """测试SQL查询语句"""
    print("测试瓶组SQL查询语句...")
    
    try:
        # 构建查询语句
        stmt = select(
            SampleRecord.id.label('sample_id'),
            SampleRecord.sample_number,
            SampleRecord.sample_type,
            SampleRecord.sample_source,
            SampleRecord.point_name,
            SampleRecord.detection_method,
            SampleRecord.detection_category,
            SampleRecord.detection_parameter
        ).select_from(SampleRecord).join(
            SamplingTaskGroup, SampleRecord.sampling_task_group_id == SamplingTaskGroup.id
        ).where(SamplingTaskGroup.sampling_task_id == 15)
        
        print("✓ SQL查询语句构建成功")
        print(f"查询语句: {stmt}")
        
        return True
        
    except Exception as e:
        print(f"✗ SQL查询语句构建失败: {e}")
        return False


def test_service_import():
    """测试服务导入"""
    print("\n测试服务导入...")
    
    try:
        from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
        print("✓ SamplingBottleGroupService 导入成功")
        
        # 测试VO模型导入
        from module_sampling.entity.vo.sampling_bottle_group_vo import SampleRecordWithDetectionModel
        print("✓ SampleRecordWithDetectionModel 导入成功")
        
        # 创建测试实例
        test_model = SampleRecordWithDetectionModel(
            sample_id=1,
            sample_number=1,
            sample_type="水样",
            sample_source="自来水",
            point_name="出水口",
            detection_method="GB/T 5750.4-2006",
            detection_category="微生物",
            detection_parameter="大肠菌群"
        )
        
        print("✓ SampleRecordWithDetectionModel 实例创建成功")
        print(f"  - 样品ID: {test_model.sample_id}")
        print(f"  - 检测方法: {test_model.detection_method}")
        
        return True
        
    except Exception as e:
        print(f"✗ 服务导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 50)
    print("瓶组SQL查询测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试SQL查询
    if not test_sql_query():
        all_passed = False
    
    # 测试服务导入
    if not test_service_import():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有测试通过！SQL查询修复成功")
    else:
        print("❌ 部分测试失败")
    print("=" * 50)
    
    return all_passed


if __name__ == "__main__":
    main()
