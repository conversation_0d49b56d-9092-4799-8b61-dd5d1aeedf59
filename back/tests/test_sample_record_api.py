"""
样品记录管理API测试
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from server import app

# 测试用的认证token
TEST_TOKEN = "test_token"
TEST_HEADERS = {"Authorization": f"Bearer {TEST_TOKEN}"}

class TestSampleRecordAPI:
    """样品记录API测试类"""
    
    def test_get_sample_records_by_assignment(self, client):
        """测试获取执行任务的样品记录列表"""
        assignment_id = 1
        response = client.get(
            f"/sampling/sample-records/assignment/{assignment_id}",
            headers=TEST_HEADERS
        )

        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")

        # 验证响应
        if response.status_code != 200:
            print(f"错误: 期望状态码200，实际得到{response.status_code}")
            return False
        data = response.json()
        if "code" not in data or "data" not in data or "msg" not in data:
            print(f"错误: 响应格式不正确")
            return False
        return True
        
    def test_get_sample_record_statistics(self, client):
        """测试获取样品记录统计"""
        assignment_id = 1
        response = client.get(
            f"/sampling/sample-records/statistics/assignment/{assignment_id}",
            headers=TEST_HEADERS
        )

        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")

        # 验证响应
        if response.status_code != 200:
            print(f"错误: 期望状态码200，实际得到{response.status_code}")
            return False
        data = response.json()
        if "code" not in data or "data" not in data:
            print(f"错误: 响应格式不正确")
            return False
        return True
        
    def test_generate_sample_records(self, client):
        """测试为执行任务生成样品记录"""
        assignment_id = 1
        response = client.post(
            f"/sampling/sample-records/generate/{assignment_id}",
            headers=TEST_HEADERS
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "code" in data
        assert "data" in data
        
    def test_create_sample_record(self, client):
        """测试创建样品记录"""
        sample_data = {
            "sampling_task_assignment_id": 1,
            "detection_cycle_item_id": 1,
            "project_quotation_item_id": 1,
            "sample_number": "TEST-001",
            "sample_type": "水样",
            "sample_source": "地表水",
            "point_name": "测试点位",
            "cycle_number": 1,
            "cycle_type": "月",
            "detection_category": "水质",
            "detection_parameter": "pH",
            "detection_method": "玻璃电极法",
            "status": 0,
            "remark": "测试样品"
        }
        
        response = client.post(
            "/sampling/sample-records/create",
            headers=TEST_HEADERS,
            json=sample_data
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "code" in data
        assert "data" in data
        
    def test_update_sample_record_status(self, client):
        """测试更新样品记录状态"""
        record_id = 1
        status_data = {"status": 1}
        
        response = client.put(
            f"/sampling/sample-records/status/{record_id}",
            headers=TEST_HEADERS,
            json=status_data
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "code" in data
        
    def test_batch_update_sample_record_status(self, client):
        """测试批量更新样品记录状态"""
        batch_data = {
            "record_ids": [1, 2, 3],
            "status": 2
        }
        
        response = client.put(
            "/sampling/sample-records/batch-status",
            headers=TEST_HEADERS,
            json=batch_data
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "code" in data
        
    def test_get_sample_record_detail(self, client):
        """测试获取样品记录详情"""
        record_id = 1
        response = client.get(
            f"/sampling/sample-records/{record_id}",
            headers=TEST_HEADERS
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert "code" in data
        assert "data" in data
        
    def test_delete_sample_record(self, client):
        """测试删除样品记录"""
        record_id = 999  # 使用一个不存在的ID进行测试
        response = client.delete(
            f"/sampling/sample-records/{record_id}",
            headers=TEST_HEADERS
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")
        
        # 删除不存在的记录应该返回错误或成功（取决于业务逻辑）
        assert response.status_code in [200, 404, 500]


if __name__ == "__main__":
    # 直接运行测试
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    client = TestClient(app)
    test_instance = TestSampleRecordAPI()

    print("=== 开始测试样品记录管理API ===")

    try:
        print("\n1. 测试获取样品记录列表...")
        result1 = test_instance.test_get_sample_records_by_assignment(client)
        print(f"结果: {'通过' if result1 else '失败'}")

        print("\n2. 测试获取样品记录统计...")
        result2 = test_instance.test_get_sample_record_statistics(client)
        print(f"结果: {'通过' if result2 else '失败'}")

        print("\n=== 主要测试完成 ===")

    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
