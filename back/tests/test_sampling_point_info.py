"""
采样点位信息功能测试
"""

import pytest
import asyncio
from decimal import Decimal
from datetime import datetime
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from config.database import get_db
from module_sampling.entity.do.sampling_point_info_do import SamplingPointInfo
from module_sampling.service.sampling_point_info_service import SamplingPointInfoService
from module_sampling.dto.sampling_point_info_dto import (
    SamplingPointInfoCreateDTO,
    SamplingPointInfoUpdateDTO
)


class TestSamplingPointInfoService:
    """采样点位信息服务测试"""
    
    @pytest.fixture
    async def db_session(self):
        """获取数据库会话"""
        async for session in get_db():
            yield session
    
    @pytest.fixture
    def sample_point_info_data(self):
        """测试用点位信息数据"""
        return {
            "sampling_task_group_id": 1,
            "point_name": "测试点位",
            "point_photo_url": "http://example.com/point.jpg",
            "point_photo_name": "point.jpg",
            "east_photo_url": "http://example.com/east.jpg",
            "east_photo_name": "east.jpg",
            "south_photo_url": "http://example.com/south.jpg",
            "south_photo_name": "south.jpg",
            "west_photo_url": "http://example.com/west.jpg",
            "west_photo_name": "west.jpg",
            "north_photo_url": "http://example.com/north.jpg",
            "north_photo_name": "north.jpg",
            "longitude": Decimal("116.397128"),
            "latitude": Decimal("39.916527"),
            "coordinate_system": "WGS84",
            "altitude": Decimal("50.5"),
            "sampling_time": datetime.now(),
            "weather_condition": "晴天",
            "temperature": Decimal("25.5"),
            "humidity": Decimal("60.0"),
            "wind_speed": Decimal("3.2"),
            "wind_direction": "东南风",
            "remarks": "测试备注信息"
        }
    
    async def test_create_point_info(self, db_session: AsyncSession, sample_point_info_data):
        """测试创建点位信息"""
        service = SamplingPointInfoService(db_session)
        create_dto = SamplingPointInfoCreateDTO(**sample_point_info_data)
        
        result = await service.create_point_info(create_dto, user_id=1)
        
        assert result is not None
        assert result.point_name == "测试点位"
        assert result.sampling_task_group_id == 1
        assert result.longitude == Decimal("116.397128")
        assert result.latitude == Decimal("39.916527")
    
    async def test_get_point_info_by_id(self, db_session: AsyncSession, sample_point_info_data):
        """测试根据ID获取点位信息"""
        service = SamplingPointInfoService(db_session)
        create_dto = SamplingPointInfoCreateDTO(**sample_point_info_data)
        
        # 先创建点位信息
        created = await service.create_point_info(create_dto, user_id=1)
        
        # 根据ID获取
        result = await service.get_point_info_by_id(created.id)
        
        assert result is not None
        assert result.id == created.id
        assert result.point_name == "测试点位"
    
    async def test_get_point_info_by_group_id(self, db_session: AsyncSession, sample_point_info_data):
        """测试根据分组ID获取点位信息"""
        service = SamplingPointInfoService(db_session)
        create_dto = SamplingPointInfoCreateDTO(**sample_point_info_data)
        
        # 先创建点位信息
        await service.create_point_info(create_dto, user_id=1)
        
        # 根据分组ID获取
        result = await service.get_point_info_by_group_id(1)
        
        assert result is not None
        assert result.sampling_task_group_id == 1
        assert result.point_name == "测试点位"
    
    async def test_update_point_info(self, db_session: AsyncSession, sample_point_info_data):
        """测试更新点位信息"""
        service = SamplingPointInfoService(db_session)
        create_dto = SamplingPointInfoCreateDTO(**sample_point_info_data)
        
        # 先创建点位信息
        created = await service.create_point_info(create_dto, user_id=1)
        
        # 更新点位信息
        update_dto = SamplingPointInfoUpdateDTO(
            point_name="更新后的点位名称",
            temperature=Decimal("30.0"),
            remarks="更新后的备注"
        )
        
        result = await service.update_point_info(created.id, update_dto, user_id=1)
        
        assert result is not None
        assert result.point_name == "更新后的点位名称"
        assert result.temperature == Decimal("30.0")
        assert result.remarks == "更新后的备注"
    
    async def test_delete_point_info(self, db_session: AsyncSession, sample_point_info_data):
        """测试删除点位信息"""
        service = SamplingPointInfoService(db_session)
        create_dto = SamplingPointInfoCreateDTO(**sample_point_info_data)
        
        # 先创建点位信息
        created = await service.create_point_info(create_dto, user_id=1)
        
        # 删除点位信息
        result = await service.delete_point_info(created.id)
        
        assert result is True
        
        # 验证已删除
        deleted = await service.get_point_info_by_id(created.id)
        assert deleted is None


class TestSamplingPointInfoAPI:
    """采样点位信息API测试"""
    
    @pytest.fixture
    def sample_api_data(self):
        """测试用API数据"""
        return {
            "samplingTaskGroupId": 1,
            "pointName": "API测试点位",
            "pointPhotoUrl": "http://example.com/api_point.jpg",
            "pointPhotoName": "api_point.jpg",
            "longitude": "116.397128",
            "latitude": "39.916527",
            "altitude": "50.5",
            "weatherCondition": "多云",
            "temperature": "22.0",
            "humidity": "65.0",
            "remarks": "API测试备注"
        }
    
    async def test_create_point_info_api(self, client: AsyncClient, sample_api_data):
        """测试创建点位信息API"""
        response = await client.post(
            "/sampling/point-info/create",
            json=sample_api_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["msg"] == "创建点位信息成功"
        assert data["data"]["pointName"] == "API测试点位"
    
    async def test_get_point_info_by_group_id_api(self, client: AsyncClient, sample_api_data):
        """测试根据分组ID获取点位信息API"""
        # 先创建点位信息
        create_response = await client.post(
            "/sampling/point-info/create",
            json=sample_api_data,
            headers={"Authorization": "Bearer test_token"}
        )
        assert create_response.status_code == 200
        
        # 根据分组ID获取
        response = await client.get(
            "/sampling/point-info/group/1",
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["pointName"] == "API测试点位"
    
    async def test_update_point_info_api(self, client: AsyncClient, sample_api_data):
        """测试更新点位信息API"""
        # 先创建点位信息
        create_response = await client.post(
            "/sampling/point-info/create",
            json=sample_api_data,
            headers={"Authorization": "Bearer test_token"}
        )
        assert create_response.status_code == 200
        point_info_id = create_response.json()["data"]["id"]
        
        # 更新点位信息
        update_data = {
            "pointName": "更新后的API点位",
            "temperature": "28.0",
            "remarks": "更新后的API备注"
        }
        
        response = await client.put(
            f"/sampling/point-info/update/{point_info_id}",
            json=update_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["pointName"] == "更新后的API点位"
    
    async def test_delete_point_info_api(self, client: AsyncClient, sample_api_data):
        """测试删除点位信息API"""
        # 先创建点位信息
        create_response = await client.post(
            "/sampling/point-info/create",
            json=sample_api_data,
            headers={"Authorization": "Bearer test_token"}
        )
        assert create_response.status_code == 200
        point_info_id = create_response.json()["data"]["id"]
        
        # 删除点位信息
        response = await client.delete(
            f"/sampling/point-info/delete/{point_info_id}",
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["msg"] == "删除点位信息成功"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
