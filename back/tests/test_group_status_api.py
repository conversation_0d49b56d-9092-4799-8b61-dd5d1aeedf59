"""
简单的API测试，验证分组状态更新功能
"""
import requests
import json

def test_group_status_api():
    """测试分组状态API"""
    base_url = "http://127.0.0.1:9099"
    headers = {"Authorization": "Bearer test_token"}
    
    print("=== 测试分组状态API ===")
    
    # 1. 获取分组列表
    print("1. 获取分组列表...")
    response = requests.get(f"{base_url}/sampling/task-group/list", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code != 200:
        print(f"获取分组列表失败: {response.text}")
        return
    
    data = response.json()
    print(f"响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    if not data.get("data"):
        print("没有找到分组数据")
        return
    
    # 选择第一个分组进行测试
    test_group = data["data"][0]
    group_id = test_group["id"]
    original_status = test_group.get("status", 0)
    
    print(f"测试分组ID: {group_id}, 原始状态: {original_status}")
    
    # 2. 更新分组状态
    print("2. 更新分组状态...")
    new_status = 1 if original_status != 1 else 2
    
    response = requests.put(
        f"{base_url}/sampling/task-group/{group_id}/status",
        json=new_status,
        headers=headers
    )
    
    print(f"更新状态码: {response.status_code}")
    print(f"更新响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    if response.status_code == 200:
        result = response.json()
        if result["code"] == 200:
            print(f"✅ 分组状态更新成功: {original_status} -> {result['data']['status']}")
        else:
            print(f"❌ 分组状态更新失败: {result['msg']}")
    else:
        print(f"❌ API调用失败: {response.text}")
        return
    
    # 3. 验证状态更新
    print("3. 验证状态更新...")
    response = requests.get(f"{base_url}/sampling/task-group/list", headers=headers)
    if response.status_code == 200:
        data = response.json()
        updated_group = None
        for group in data["data"]:
            if group["id"] == group_id:
                updated_group = group
                break
        
        if updated_group:
            print(f"✅ 验证成功: 分组 {group_id} 状态为 {updated_group['status']}")
        else:
            print(f"❌ 未找到分组 {group_id}")
    
    # 4. 恢复原始状态
    print("4. 恢复原始状态...")
    response = requests.put(
        f"{base_url}/sampling/task-group/{group_id}/status",
        json=original_status,
        headers=headers
    )
    
    if response.status_code == 200:
        print(f"✅ 状态已恢复为: {original_status}")
    else:
        print(f"❌ 恢复状态失败: {response.text}")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    try:
        test_group_status_api()
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保服务已启动")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
