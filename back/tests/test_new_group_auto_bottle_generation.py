#!/usr/bin/env python3
"""
测试新分组的自动瓶组生成功能
"""

import asyncio
import requests
import json

# 测试配置
BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def test_new_group_auto_bottle_generation():
    """测试新分组的自动瓶组生成"""
    
    print("=" * 60)
    print("测试新分组的自动瓶组生成功能")
    print("=" * 60)
    
    # 1. 获取所有分组，找一个状态为0（待执行）的分组
    print("查找待执行的分组...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/list?page_num=1&page_size=50",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            groups_data = data.get('data', [])
            if isinstance(groups_data, dict):
                groups = groups_data.get('rows', [])
            else:
                groups = groups_data
            print(f"✓ 获取到 {len(groups)} 个分组")
            
            # 找一个状态为0的分组
            pending_groups = [g for g in groups if g.get('status') == 0]
            
            if not pending_groups:
                print("✗ 没有找到待执行的分组")
                return
                
            test_group = pending_groups[0]
            group_id = test_group['id']
            task_id = test_group['samplingTaskId']
            
            print(f"  - 选择测试分组: ID={group_id}, 任务ID={task_id}")
            print(f"  - 分组信息: {test_group.get('pointName', 'N/A')} - {test_group.get('detectionCategory', 'N/A')}")
            
        else:
            print(f"✗ 获取分组列表失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"✗ 获取分组列表异常: {e}")
        return
    
    # 2. 检查该分组是否已有样品记录
    print(f"\n检查分组 {group_id} 是否已有样品记录...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/sample-records/group/{group_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            existing_records = data.get('data', [])
            
            if existing_records:
                print(f"✗ 分组 {group_id} 已有 {len(existing_records)} 个样品记录，跳过测试")
                return
            else:
                print(f"✓ 分组 {group_id} 暂无样品记录，可以进行测试")
                
        else:
            print(f"✗ 检查样品记录失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"✗ 检查样品记录异常: {e}")
        return
    
    # 3. 检查任务是否已有瓶组记录
    print(f"\n检查任务 {task_id} 是否已有瓶组记录...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            existing_bottles = data.get('data', [])
            print(f"✓ 任务 {task_id} 当前有 {len(existing_bottles)} 个瓶组")
            
        else:
            print(f"✗ 检查瓶组记录失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 检查瓶组记录异常: {e}")
    
    # 4. 为分组生成样品记录（应该自动生成瓶组）
    print(f"\n为分组 {group_id} 生成样品记录（应该自动生成瓶组）...")
    try:
        response = requests.post(
            f"{BASE_URL}/sampling/sample-records/generate/group/{group_id}",
            headers=HEADERS
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ 样品记录生成成功")
            
            sample_records = data.get('data', [])
            print(f"  - 生成样品记录数: {len(sample_records)}")
            
            for i, record in enumerate(sample_records[:3], 1):
                print(f"    样品{i}: ID={record.get('id')}, 编号={record.get('sampleNumber')}")
                
            if len(sample_records) > 3:
                print(f"    ... 还有 {len(sample_records) - 3} 个样品记录")
                
        else:
            print(f"✗ 样品记录生成失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return
            
    except Exception as e:
        print(f"✗ 生成样品记录异常: {e}")
        return
    
    # 5. 检查是否自动生成了瓶组
    print(f"\n检查任务 {task_id} 是否自动生成了瓶组...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            bottle_groups = data.get('data', [])
            print(f"✓ 瓶组记录获取成功")
            print(f"  - 瓶组总数: {len(bottle_groups)}")
            
            if bottle_groups:
                # 统计瓶组类型
                default_count = sum(1 for g in bottle_groups if not g.get('bottleMaintenanceId'))
                matched_count = len(bottle_groups) - default_count
                
                print(f"  - 默认瓶组: {default_count}")
                print(f"  - 匹配瓶组: {matched_count}")
                
                print("  - 瓶组列表:")
                for i, group in enumerate(bottle_groups[:5], 1):
                    bottle_type = "匹配瓶组" if group.get('bottleMaintenanceId') else "默认瓶组"
                    print(f"    瓶组{i}: {group.get('bottleGroupCode')} - {bottle_type}")
                    print(f"           检测方法: {group.get('detectionMethod', 'N/A')[:50]}...")
                    
                if len(bottle_groups) > 5:
                    print(f"    ... 还有 {len(bottle_groups) - 5} 个瓶组")
                    
            else:
                print("  - 没有瓶组记录")
                
        else:
            print(f"✗ 获取瓶组记录失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 获取瓶组记录异常: {e}")
    
    print("\n" + "=" * 60)
    if len(bottle_groups) > len(existing_bottles):
        print("✅ 测试成功！样品生成时自动生成了瓶组")
        print(f"   新增瓶组数: {len(bottle_groups) - len(existing_bottles)}")
    else:
        print("❌ 测试失败！样品生成时没有自动生成瓶组")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_new_group_auto_bottle_generation())
