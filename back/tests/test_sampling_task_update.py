#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采样任务更新功能测试
"""

import pytest
import json
from httpx import AsyncClient
from datetime import datetime


class TestSamplingTaskUpdate:
    """采样任务更新测试类"""
    
    @pytest.mark.asyncio
    async def test_update_sampling_task_success(self, async_client: AsyncClient):
        """测试成功更新采样任务"""
        # 准备测试数据
        task_id = 10
        update_data = {
            "taskName": "测试任务更新",
            "description": "更新后的描述",
            "status": 1,
            "plannedStartDate": "2025-07-28",
            "plannedEndDate": "2025-07-30",
            "remarks": "测试备注"
        }
        
        # 发送更新请求
        response = await async_client.put(
            f"/sampling/task/update/{task_id}",
            json=update_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # 验证响应
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        assert result["msg"] == "操作成功"
        
        # 验证更新后的数据
        get_response = await async_client.get(
            f"/sampling/task/{task_id}",
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert get_response.status_code == 200
        task_data = get_response.json()["data"]
        assert task_data["taskName"] == update_data["taskName"]
        assert task_data["description"] == update_data["description"]
        assert task_data["status"] == update_data["status"]
        assert task_data["remarks"] == update_data["remarks"]
    
    @pytest.mark.asyncio
    async def test_update_sampling_task_not_found(self, async_client: AsyncClient):
        """测试更新不存在的采样任务"""
        # 使用不存在的任务ID
        task_id = 99999
        update_data = {
            "taskName": "测试任务更新",
            "description": "更新后的描述"
        }
        
        # 发送更新请求
        response = await async_client.put(
            f"/sampling/task/update/{task_id}",
            json=update_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # 验证响应
        assert response.status_code == 404
        result = response.json()
        assert "不存在" in result["msg"]
    
    @pytest.mark.asyncio
    async def test_update_sampling_task_partial_fields(self, async_client: AsyncClient):
        """测试部分字段更新"""
        task_id = 10
        update_data = {
            "description": "仅更新描述字段"
        }
        
        # 获取更新前的数据
        get_response_before = await async_client.get(
            f"/sampling/task/{task_id}",
            headers={"Authorization": "Bearer test_token"}
        )
        task_data_before = get_response_before.json()["data"]
        
        # 发送更新请求
        response = await async_client.put(
            f"/sampling/task/update/{task_id}",
            json=update_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # 验证响应
        assert response.status_code == 200
        
        # 验证更新后的数据
        get_response_after = await async_client.get(
            f"/sampling/task/{task_id}",
            headers={"Authorization": "Bearer test_token"}
        )
        task_data_after = get_response_after.json()["data"]
        
        # 验证只有description字段被更新，其他字段保持不变
        assert task_data_after["description"] == update_data["description"]
        assert task_data_after["taskName"] == task_data_before["taskName"]
        assert task_data_after["status"] == task_data_before["status"]
    
    @pytest.mark.asyncio
    async def test_update_sampling_task_with_complex_data(self, async_client: AsyncClient):
        """测试使用复杂数据更新采样任务"""
        task_id = 10
        update_data = {
            "id": 10,
            "projectQuotationId": 16,
            "taskName": "测试项目-采样任务",
            "taskCode": "25070005",
            "taskDescription": "",
            "plannedStartDate": "2025-07-28",
            "plannedEndDate": "2025-07-30",
            "actualStartDate": None,
            "actualEndDate": None,
            "samplingLocation": None,
            "samplingMethod": None,
            "sampleCount": None,
            "status": 0,
            "statusLabel": "待执行",
            "isUrgent": False,
            "remarks": None,
            "createBy": 1,
            "createTime": "2025-07-26T22:29:58",
            "updateBy": 1,
            "updateTime": "2025-07-27T11:02:25",
            "projectName": "测试项目",
            "assignedUserName": None,
            "assignedUserId": 104,
            "description": "烦烦烦",
            "remark": "烦烦烦"
        }
        
        # 发送更新请求
        response = await async_client.put(
            f"/sampling/task/update/{task_id}",
            json=update_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # 验证响应
        assert response.status_code == 200
        result = response.json()
        assert result["code"] == 200
        
        # 验证更新后的数据
        get_response = await async_client.get(
            f"/sampling/task/{task_id}",
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert get_response.status_code == 200
        task_data = get_response.json()["data"]
        assert task_data["description"] == update_data["description"]
        assert task_data["taskName"] == update_data["taskName"]