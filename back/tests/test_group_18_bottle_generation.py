#!/usr/bin/env python3
"""
测试分组18的瓶组生成问题
"""

import asyncio
import requests
import json

# 测试配置
BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def test_group_18_bottle_generation():
    """测试分组18的瓶组生成"""
    
    print("=" * 60)
    print("测试分组18的瓶组生成问题")
    print("=" * 60)
    
    group_id = 18
    task_id = 12
    
    # 1. 检查分组18的详细信息
    print(f"检查分组 {group_id} 的详细信息...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/task-group/{group_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            group_info = data.get('data', {})
            print(f"✓ 分组信息获取成功")
            print(f"  - 分组ID: {group_info.get('id')}")
            print(f"  - 任务ID: {group_info.get('samplingTaskId')}")
            print(f"  - 周期编号: {group_info.get('cycleNumber')}")
            print(f"  - 周期类型: {group_info.get('cycleType')}")
            print(f"  - 检测类别: {group_info.get('detectionCategory')}")
            print(f"  - 点位名称: {group_info.get('pointName')}")
            print(f"  - 状态: {group_info.get('status')}")
        else:
            print(f"✗ 获取分组信息失败，状态码: {response.status_code}")
            return
            
    except Exception as e:
        print(f"✗ 获取分组信息异常: {e}")
        return
    
    # 2. 检查分组18的样品记录
    print(f"\n检查分组 {group_id} 的样品记录...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/sample-records/group/{group_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            sample_records = data.get('data', [])
            print(f"✓ 样品记录获取成功")
            print(f"  - 样品记录数: {len(sample_records)}")
            
            for i, record in enumerate(sample_records, 1):
                print(f"    样品{i}: ID={record.get('id')}, 编号={record.get('sampleNumber')}")
                print(f"           检测方法: {record.get('detectionMethod', 'N/A')}")
                
        else:
            print(f"✗ 获取样品记录失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 获取样品记录异常: {e}")
    
    # 3. 检查任务12的瓶组记录
    print(f"\n检查任务 {task_id} 的瓶组记录...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            bottle_groups = data.get('data', [])
            print(f"✓ 瓶组记录获取成功")
            print(f"  - 瓶组数: {len(bottle_groups)}")
            
            if bottle_groups:
                for i, group in enumerate(bottle_groups, 1):
                    print(f"    瓶组{i}: {group.get('bottleGroupCode')} - {group.get('detectionMethod', 'N/A')[:50]}...")
            else:
                print("  - 暂无瓶组记录")
                
        else:
            print(f"✗ 获取瓶组记录失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 获取瓶组记录异常: {e}")
    
    # 4. 手动为任务12生成瓶组
    print(f"\n手动为任务 {task_id} 生成瓶组...")
    try:
        response = requests.post(
            f"{BASE_URL}/sampling/bottle-groups/generate/{task_id}",
            headers=HEADERS
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ 瓶组生成成功")
            
            result = data.get('data', {})
            print(f"  - 生成瓶组总数: {result.get('totalGroups', 0)}")
            print(f"  - 默认瓶组数: {result.get('defaultGroups', 0)}")
            print(f"  - 匹配瓶组数: {result.get('matchedGroups', 0)}")
            
            bottle_groups = result.get('bottleGroups', [])
            if bottle_groups:
                print("  - 瓶组详情:")
                for i, group in enumerate(bottle_groups[:3], 1):
                    print(f"    瓶组{i}: {group.get('bottleGroupCode')} - {group.get('detectionMethod', 'N/A')[:50]}...")
                    
                if len(bottle_groups) > 3:
                    print(f"    ... 还有 {len(bottle_groups) - 3} 个瓶组")
                    
        else:
            print(f"✗ 瓶组生成失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"✗ 瓶组生成异常: {e}")
    
    # 5. 再次检查任务12的瓶组记录
    print(f"\n再次检查任务 {task_id} 的瓶组记录...")
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            data = response.json()
            bottle_groups = data.get('data', [])
            print(f"✓ 瓶组记录获取成功")
            print(f"  - 瓶组数: {len(bottle_groups)}")
            
            if bottle_groups:
                print("  - 瓶组列表:")
                for i, group in enumerate(bottle_groups, 1):
                    bottle_type = "匹配瓶组" if group.get('bottleMaintenanceId') else "默认瓶组"
                    print(f"    瓶组{i}: {group.get('bottleGroupCode')} - {bottle_type}")
                    print(f"           检测方法: {group.get('detectionMethod', 'N/A')[:50]}...")
            else:
                print("  - 仍然没有瓶组记录")
                
        else:
            print(f"✗ 获取瓶组记录失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 获取瓶组记录异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_group_18_bottle_generation())
