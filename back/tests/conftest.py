"""
测试配置文件
"""
import asyncio
import os
import pytest
import pytest_asyncio
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy import text, Column, Integer, String, DateTime, UniqueConstraint
from sqlalchemy.sql import func
from typing import AsyncGenerator, Generator

from config.get_db import get_db
from server import app as main_app
from utils.log_util import logger

# 创建独立的测试Base，避免导入所有表
TestBase = declarative_base()

class SamplingTaskSequenceModel(TestBase):
    """测试用的采样任务编号序列表"""
    __tablename__ = 'sampling_task_sequence'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False, comment='主键ID')
    
    # 业务字段
    year_month = Column(String(4), nullable=False, comment='年月（YYMM）')
    sequence_number = Column(Integer, default=0, comment='当月序号')
    
    # 审计字段
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 约束
    __table_args__ = (
        UniqueConstraint('year_month', name='uk_year_month'),
        {'comment': '采样任务编号序列表'}
    )

@pytest_asyncio.fixture(scope="module")
async def test_engine():
    # Create test database URL for MySQL
    from urllib.parse import quote_plus
    
    # MySQL test database configuration
    TEST_DB_HOST = os.environ.get("TEST_DB_HOST", "127.0.0.1")
    TEST_DB_PORT = os.environ.get("TEST_DB_PORT", "3306")
    TEST_DB_USERNAME = os.environ.get("TEST_DB_USERNAME", "lims-user")
    TEST_DB_PASSWORD = os.environ.get("TEST_DB_PASSWORD", "lims-Root1")
    TEST_DB_DATABASE = os.environ.get("TEST_DB_DATABASE", "test")
    
    TEST_DATABASE_URL = (
        f'mysql+asyncmy://{TEST_DB_USERNAME}:{quote_plus(TEST_DB_PASSWORD)}@'
        f'{TEST_DB_HOST}:{TEST_DB_PORT}/{TEST_DB_DATABASE}'
    )
    
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    async with engine.begin() as conn:
        # 禁用外键检查
        await conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
        
        # 删除可能存在的表
        await conn.execute(text("DROP TABLE IF EXISTS sampling_task_sequence"))
        
        # 只创建测试需要的表
        await conn.run_sync(TestBase.metadata.create_all)
        
        # 保持外键检查禁用状态
        # await conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
    yield engine
    await engine.dispose()

@pytest_asyncio.fixture(scope="function")
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    async with test_engine.connect() as connection:
        async with connection.begin() as transaction:
            # 在每个测试会话中禁用外键检查
            await connection.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            async_session = sessionmaker(
                test_engine, class_=AsyncSession, expire_on_commit=False
            )
            async with async_session(bind=connection) as session:
                yield session
            await transaction.rollback()


@pytest.fixture
def app(db_session: AsyncSession) -> FastAPI:
    """
    创建测试应用

    :param db_session: 测试数据库会话
    :return: 测试应用
    """
    # 覆盖依赖项
    async def override_get_db():
        yield db_session

    main_app.dependency_overrides[get_db] = override_get_db
    return main_app


@pytest.fixture
def client(app: FastAPI) -> Generator[TestClient, None, None]:
    """
    创建测试客户端

    :param app: 测试应用
    :return: 测试客户端
    """
    with TestClient(app) as client:
        yield client
