"""
设备管理VO模型
"""

from typing import List, Optional, Union
from datetime import date, datetime
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict, field_validator
from pydantic.alias_generators import to_camel


class AttachmentModel(BaseModel):
    """附件模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    file_name: str = Field(..., description="文件名称")
    file_path: str = Field(..., description="文件路径")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")
    upload_time: Optional[str] = Field(None, description="上传时间")
    unique_id: Optional[str] = Field(None, description="全局唯一编号")


class EquipmentManagementModel(BaseModel):
    """设备管理基础模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(None, description="主键ID")

    # 基础信息类
    equipment_number: str = Field(..., description="设备编号", max_length=100)
    equipment_name: str = Field(..., description="测量设备（器具）名称", max_length=200)
    enable_date: Optional[date] = Field(None, description="启用日期")
    equipment_type: Optional[str] = Field(None, description="设备类型", max_length=100)
    model_specification: Optional[str] = Field(None, description="型号规格", max_length=200)
    factory_number: Optional[str] = Field(None, description="出厂编号", max_length=100)
    manufacturer: Optional[str] = Field(None, description="制造商", max_length=200)

    # 技术参数类
    indicator_characteristics: Optional[str] = Field(None, description="指标特性")

    # 校准/溯源管理类
    calibration_institution: Optional[str] = Field(None, description="检定/校准机构", max_length=200)
    traceability_method: Optional[str] = Field(None, description="溯源方式", max_length=100)
    current_calibration_date: Optional[date] = Field(None, description="本次校准/核查日期")
    certificate_number: Optional[str] = Field(None, description="证书编号", max_length=100)
    next_calibration_date: Optional[date] = Field(None, description="下次校准/核查日期")
    interval_days: Optional[str] = Field(None, description="间隔日期（天数）")
    interim_check_date: Optional[date] = Field(None, description="期间核查日期")
    calibration_remark: Optional[str] = Field(None, description="备注（校准确认）")
    calibration_content: Optional[str] = Field(None, description="校准内容")
    attachments: Optional[List[AttachmentModel]] = Field(default=[], description="附件列表")

    @field_validator("interval_days", mode="before")
    @classmethod
    def validate_interval_days(cls, v):
        """将数字类型的间隔日期转换为字符串"""
        if v is None:
            return v
        if isinstance(v, (int, float)):
            return str(v)
        return v

    # 管理责任类
    manager: Optional[str] = Field(None, description="管理者", max_length=100)
    equipment_status: Optional[str] = Field(None, description="设备状态", max_length=50)
    equipment_status_description: Optional[str] = Field(None, description="设备状态说明")
    location: Optional[str] = Field(None, description="放置地点", max_length=200)

    # 财务与合同类
    amount: Optional[Decimal] = Field(None, description="金额")
    contract: Optional[str] = Field(None, description="合同", max_length=200)
    invoice: Optional[str] = Field(None, description="发票", max_length=200)


class AddEquipmentManagementModel(BaseModel):
    """添加设备管理模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    # 基础信息类
    equipment_number: str = Field(..., description="设备编号", max_length=100)
    equipment_name: str = Field(..., description="测量设备（器具）名称", max_length=200)
    enable_date: Optional[date] = Field(None, description="启用日期")
    equipment_type: Optional[str] = Field(None, description="设备类型", max_length=100)
    model_specification: Optional[str] = Field(None, description="型号规格", max_length=200)
    factory_number: Optional[str] = Field(None, description="出厂编号", max_length=100)
    manufacturer: Optional[str] = Field(None, description="制造商", max_length=200)

    # 技术参数类
    indicator_characteristics: Optional[str] = Field(None, description="指标特性")

    # 校准/溯源管理类
    calibration_institution: Optional[str] = Field(None, description="检定/校准机构", max_length=200)
    traceability_method: Optional[str] = Field(None, description="溯源方式", max_length=100)
    current_calibration_date: Optional[date] = Field(None, description="本次校准/核查日期")
    certificate_number: Optional[str] = Field(None, description="证书编号", max_length=100)
    next_calibration_date: Optional[date] = Field(None, description="下次校准/核查日期")
    interval_days: Optional[str] = Field(None, description="间隔日期（天数）")
    interim_check_date: Optional[date] = Field(None, description="期间核查日期")
    calibration_remark: Optional[str] = Field(None, description="备注（校准确认）")
    calibration_content: Optional[str] = Field(None, description="校准内容")
    attachments: Optional[List[AttachmentModel]] = Field(default=[], description="附件列表")

    @field_validator("interval_days", mode="before")
    @classmethod
    def validate_interval_days(cls, v):
        """将数字类型的间隔日期转换为字符串"""
        if v is None:
            return v
        if isinstance(v, (int, float)):
            return str(v)
        return v

    @field_validator("equipment_status_description")
    @classmethod
    def validate_status_description(cls, v, info):
        """验证设备状态说明：当设备状态为停用时，状态说明为必填"""
        if hasattr(info, 'data') and info.data:
            equipment_status = info.data.get('equipment_status')
            if equipment_status == '停用' and (not v or not v.strip()):
                raise ValueError("当设备状态为停用时，设备状态说明为必填项")
        return v

    # 管理责任类
    manager: Optional[str] = Field(None, description="管理者", max_length=100)
    equipment_status: Optional[str] = Field(None, description="设备状态", max_length=50)
    equipment_status_description: Optional[str] = Field(None, description="设备状态说明")
    location: Optional[str] = Field(None, description="放置地点", max_length=200)

    # 财务与合同类
    amount: Optional[Decimal] = Field(None, description="金额")
    contract: Optional[str] = Field(None, description="合同", max_length=200)
    invoice: Optional[str] = Field(None, description="发票", max_length=200)


class EditEquipmentManagementModel(BaseModel):
    """编辑设备管理模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: int = Field(..., description="主键ID")

    # 基础信息类
    equipment_number: str = Field(..., description="设备编号", max_length=100)
    equipment_name: str = Field(..., description="测量设备（器具）名称", max_length=200)
    enable_date: Optional[date] = Field(None, description="启用日期")
    equipment_type: Optional[str] = Field(None, description="设备类型", max_length=100)
    model_specification: Optional[str] = Field(None, description="型号规格", max_length=200)
    factory_number: Optional[str] = Field(None, description="出厂编号", max_length=100)
    manufacturer: Optional[str] = Field(None, description="制造商", max_length=200)

    # 技术参数类
    indicator_characteristics: Optional[str] = Field(None, description="指标特性")

    # 校准/溯源管理类
    calibration_institution: Optional[str] = Field(None, description="检定/校准机构", max_length=200)
    traceability_method: Optional[str] = Field(None, description="溯源方式", max_length=100)
    current_calibration_date: Optional[date] = Field(None, description="本次校准/核查日期")
    certificate_number: Optional[str] = Field(None, description="证书编号", max_length=100)
    next_calibration_date: Optional[date] = Field(None, description="下次校准/核查日期")
    interval_days: Optional[str] = Field(None, description="间隔日期（天数）")
    interim_check_date: Optional[date] = Field(None, description="期间核查日期")
    calibration_remark: Optional[str] = Field(None, description="备注（校准确认）")
    calibration_content: Optional[str] = Field(None, description="校准内容")
    attachments: Optional[List[AttachmentModel]] = Field(default=[], description="附件列表")

    @field_validator("interval_days", mode="before")
    @classmethod
    def validate_interval_days(cls, v):
        """将数字类型的间隔日期转换为字符串"""
        if v is None:
            return v
        if isinstance(v, (int, float)):
            return str(v)
        return v

    # 管理责任类
    manager: Optional[str] = Field(None, description="管理者", max_length=100)
    equipment_status: Optional[str] = Field(None, description="设备状态", max_length=50)
    equipment_status_description: Optional[str] = Field(None, description="设备状态说明")
    location: Optional[str] = Field(None, description="放置地点", max_length=200)

    # 财务与合同类
    amount: Optional[Decimal] = Field(None, description="金额")
    contract: Optional[str] = Field(None, description="合同", max_length=200)
    invoice: Optional[str] = Field(None, description="发票", max_length=200)

    @field_validator("equipment_status_description")
    @classmethod
    def validate_status_description(cls, v, info):
        """验证设备状态说明：当设备状态为停用时，状态说明为必填"""
        if hasattr(info, 'data') and info.data:
            equipment_status = info.data.get('equipment_status')
            if equipment_status == '停用' and (not v or not v.strip()):
                raise ValueError("当设备状态为停用时，设备状态说明为必填项")
        return v


class EquipmentManagementQueryModel(BaseModel):
    """设备管理查询模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)
    page_num: Optional[int] = Field(default=1, description="页码")
    page_size: Optional[int] = Field(default=10, description="每页数量")
    equipment_number: Optional[str] = Field(None, description="设备编号")
    equipment_name: Optional[str] = Field(None, description="设备名称")
    equipment_type: Optional[str] = Field(None, description="设备类型")
    equipment_status: Optional[str] = Field(None, description="设备状态")
    manager: Optional[str] = Field(None, description="管理者")
    manufacturer: Optional[str] = Field(None, description="制造商")


class EquipmentManagementResponseModel(BaseModel):
    """设备管理响应模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: int = Field(..., description="主键ID")

    # 基础信息类
    equipment_number: str = Field(..., description="设备编号")
    equipment_name: str = Field(..., description="测量设备（器具）名称")
    enable_date: Optional[date] = Field(None, description="启用日期")
    equipment_type: Optional[str] = Field(None, description="设备类型")
    model_specification: Optional[str] = Field(None, description="型号规格")
    factory_number: Optional[str] = Field(None, description="出厂编号")
    manufacturer: Optional[str] = Field(None, description="制造商")

    # 技术参数类
    indicator_characteristics: Optional[str] = Field(None, description="指标特性")

    # 校准/溯源管理类
    calibration_institution: Optional[str] = Field(None, description="检定/校准机构")
    traceability_method: Optional[str] = Field(None, description="溯源方式")
    current_calibration_date: Optional[date] = Field(None, description="本次校准/核查日期")
    certificate_number: Optional[str] = Field(None, description="证书编号")
    next_calibration_date: Optional[date] = Field(None, description="下次校准/核查日期")
    interval_days: Optional[str] = Field(None, description="间隔日期（天数）")
    interim_check_date: Optional[date] = Field(None, description="期间核查日期")
    calibration_remark: Optional[str] = Field(None, description="备注（校准确认）")
    calibration_content: Optional[str] = Field(None, description="校准内容")
    attachments: Optional[List[AttachmentModel]] = Field(default=[], description="附件列表")

    # 管理责任类
    manager: Optional[str] = Field(None, description="管理者")
    equipment_status: Optional[str] = Field(None, description="设备状态")
    equipment_status_description: Optional[str] = Field(None, description="设备状态说明")
    location: Optional[str] = Field(None, description="放置地点")

    # 财务与合同类
    amount: Optional[Decimal] = Field(None, description="金额")
    contract: Optional[str] = Field(None, description="合同")
    invoice: Optional[str] = Field(None, description="发票")


class EquipmentManagementImportModel(BaseModel):
    """设备管理批量导入模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    # 按照导入字段顺序定义
    equipment_number: str = Field(..., description="设备编号")
    equipment_name: str = Field(..., description="测量设备（器具）名称")
    enable_date: Optional[str] = Field(None, description="启用日期")
    equipment_type: Optional[str] = Field(None, description="设备类型")
    model_specification: Optional[str] = Field(None, description="型号规格")
    factory_number: Optional[str] = Field(None, description="出厂编号")
    manufacturer: Optional[str] = Field(None, description="制造商")
    indicator_characteristics: Optional[str] = Field(None, description="指标特性")
    calibration_institution: Optional[str] = Field(None, description="检定/校准机构")
    traceability_method: Optional[str] = Field(None, description="溯源方式")
    current_calibration_date: Optional[str] = Field(None, description="本次校准/核查日期")
    certificate_number: Optional[str] = Field(None, description="证书编号")
    next_calibration_date: Optional[str] = Field(None, description="下次校准/核查日期")
    interval_days: Optional[str] = Field(None, description="间隔日期")
    interim_check_date: Optional[str] = Field(None, description="期间核查日期")
    manager: Optional[str] = Field(None, description="管理者")
    equipment_status: Optional[str] = Field(None, description="设备状态")
    location: Optional[str] = Field(None, description="放置地点")
    calibration_remark: Optional[str] = Field(None, description="备注（校准确认）")
    calibration_content: Optional[str] = Field(None, description="校准内容")
    amount: Optional[str] = Field(None, description="金额")
    contract: Optional[str] = Field(None, description="合同")
    invoice: Optional[str] = Field(None, description="发票")


class EquipmentManagementImportErrorModel(BaseModel):
    """设备管理导入错误模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    row: int = Field(..., description="错误行号")
    field: str = Field(..., description="错误字段")
    message: str = Field(..., description="错误信息")


class EquipmentManagementImportResultModel(BaseModel):
    """设备管理导入结果模型"""

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    success_count: int = Field(default=0, description="成功导入数量")
    error_count: int = Field(default=0, description="失败导入数量")
    errors: List[EquipmentManagementImportErrorModel] = Field(default=[], description="错误列表")
    data: List[EquipmentManagementResponseModel] = Field(default=[], description="成功导入的数据")
