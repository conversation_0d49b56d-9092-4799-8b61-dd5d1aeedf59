-- 创建采样点位信息表
-- 执行时间: 2024-12-XX

-- 创建采样点位信息表
CREATE TABLE IF NOT EXISTS sampling_point_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    
    -- 关联字段
    sampling_task_group_id BIGINT NOT NULL COMMENT '采样任务分组ID',
    point_name VARCHAR(200) COMMENT '点位名称',
    
    -- 点位照片信息
    point_photo_url VARCHAR(500) COMMENT '采样点位照片URL',
    point_photo_name VARCHAR(200) COMMENT '采样点位照片文件名',
    
    -- 环境照片信息（东南西北4张）
    east_photo_url VARCHAR(500) COMMENT '东侧环境照片URL',
    east_photo_name VARCHAR(200) COMMENT '东侧环境照片文件名',
    south_photo_url VARCHAR(500) COMMENT '南侧环境照片URL',
    south_photo_name VARCHAR(200) COMMENT '南侧环境照片文件名',
    west_photo_url VARCHAR(500) COMMENT '西侧环境照片URL',
    west_photo_name VARCHAR(200) COMMENT '西侧环境照片文件名',
    north_photo_url VARCHAR(500) COMMENT '北侧环境照片URL',
    north_photo_name VARCHAR(200) COMMENT '北侧环境照片文件名',
    
    -- 经纬度信息
    longitude DECIMAL(10, 7) COMMENT '经度',
    latitude DECIMAL(10, 7) COMMENT '纬度',
    coordinate_system VARCHAR(50) DEFAULT 'WGS84' COMMENT '坐标系统',
    
    -- 其他信息
    altitude DECIMAL(8, 2) COMMENT '海拔高度（米）',
    sampling_time DATETIME COMMENT '采样时间',
    weather_condition VARCHAR(100) COMMENT '天气状况',
    temperature DECIMAL(5, 2) COMMENT '温度（摄氏度）',
    humidity DECIMAL(5, 2) COMMENT '湿度（%）',
    wind_speed DECIMAL(5, 2) COMMENT '风速（m/s）',
    wind_direction VARCHAR(20) COMMENT '风向',
    remarks TEXT COMMENT '备注信息',
    
    -- 审计字段
    create_by BIGINT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by BIGINT COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_sampling_task_group_id (sampling_task_group_id),
    INDEX idx_point_name (point_name),
    INDEX idx_create_time (create_time),
    
    -- 外键约束
    FOREIGN KEY (sampling_task_group_id) REFERENCES sampling_task_group(id) ON DELETE CASCADE,
    FOREIGN KEY (create_by) REFERENCES sys_user(user_id),
    FOREIGN KEY (update_by) REFERENCES sys_user(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样点位信息表';

-- 添加唯一约束：每个分组只能有一条点位信息记录
ALTER TABLE sampling_point_info ADD UNIQUE KEY uk_task_group_point (sampling_task_group_id);
