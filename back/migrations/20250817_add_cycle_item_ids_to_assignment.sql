-- 为采样任务执行人指派表添加周期条目ID字段
-- 执行时间: 2025-08-17
-- 描述: 在SamplingTaskAssignment表中添加cycle_item_ids字段，用于记录执行任务关联的周期条目ID列表

-- 添加新字段
ALTER TABLE sampling_task_executor_assignment 
ADD COLUMN cycle_item_ids TEXT COMMENT '关联的周期条目ID列表（JSON格式）';

-- 为现有记录填充周期条目ID（可选，如果需要的话）
-- 这个更新语句会根据现有的分组信息查找并填充周期条目ID
-- 注意：这个操作可能比较耗时，建议在维护窗口执行

UPDATE sampling_task_executor_assignment sta
SET cycle_item_ids = (
    SELECT JSON_ARRAYAGG(dci.id)
    FROM detection_cycle_item dci
    JOIN sampling_task_cycle_item stci ON dci.id = stci.detection_cycle_item_id
    JOIN project_quotation_item pqi ON dci.project_quotation_item_id = pqi.id
    WHERE stci.sampling_task_id = sta.sampling_task_id
      AND dci.cycle_number = sta.cycle_number
      AND pqi.category = sta.detection_category
      AND pqi.point_name = sta.point_name
)
WHERE sta.cycle_item_ids IS NULL;
