-- 创建采样瓶组相关表
-- 执行时间: 2025-01-18
-- 描述: 创建瓶组与检测方法关联表、采样瓶组表、瓶组样品关联表

-- 1. 创建瓶组管理与检测方法关联表
CREATE TABLE IF NOT EXISTS `bottle_maintenance_method` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bottle_maintenance_id` int NOT NULL COMMENT '瓶组管理ID',
  `detection_method` varchar(200) NOT NULL COMMENT '检测方法',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bottle_method` (`bottle_maintenance_id`, `detection_method`),
  KEY `idx_bottle_maintenance_id` (`bottle_maintenance_id`),
  KEY `idx_detection_method` (`detection_method`),
  CONSTRAINT `fk_bottle_method_bottle` FOREIGN KEY (`bottle_maintenance_id`) 
    REFERENCES `bottle_maintenance` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='瓶组管理与检测方法关联表';

-- 2. 创建采样瓶组表
CREATE TABLE IF NOT EXISTS `sampling_bottle_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `bottle_group_code` varchar(50) NOT NULL COMMENT '瓶组编号',
  `bottle_maintenance_id` int DEFAULT 0 COMMENT '瓶组管理ID，0表示默认瓶组',
  `detection_method` varchar(200) DEFAULT NULL COMMENT '检测方法',
  `sample_count` int DEFAULT 0 COMMENT '关联样品数量',
  `status` int DEFAULT 0 COMMENT '状态：0-待采集，1-已采集，2-已送检',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bottle_group_code` (`bottle_group_code`),
  KEY `idx_sampling_task_id` (`sampling_task_id`),
  KEY `idx_bottle_maintenance_id` (`bottle_maintenance_id`),
  KEY `idx_detection_method` (`detection_method`),
  CONSTRAINT `fk_sampling_bottle_group_task` FOREIGN KEY (`sampling_task_id`) 
    REFERENCES `sampling_task` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sampling_bottle_group_bottle` FOREIGN KEY (`bottle_maintenance_id`) 
    REFERENCES `bottle_maintenance` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_sampling_bottle_group_create_by` FOREIGN KEY (`create_by`) 
    REFERENCES `sys_user` (`user_id`),
  CONSTRAINT `fk_sampling_bottle_group_update_by` FOREIGN KEY (`update_by`) 
    REFERENCES `sys_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样瓶组表';

-- 3. 创建瓶组样品关联表
CREATE TABLE IF NOT EXISTS `sampling_bottle_group_sample` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bottle_group_id` bigint NOT NULL COMMENT '瓶组ID',
  `sample_record_id` bigint NOT NULL COMMENT '样品记录ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bottle_sample` (`bottle_group_id`, `sample_record_id`),
  KEY `idx_sample_record_id` (`sample_record_id`),
  CONSTRAINT `fk_bottle_group_sample_group` FOREIGN KEY (`bottle_group_id`) 
    REFERENCES `sampling_bottle_group` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_bottle_group_sample_record` FOREIGN KEY (`sample_record_id`) 
    REFERENCES `sample_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='瓶组样品关联表';

-- 4. 创建瓶组编号序列表（用于生成瓶组序号）
CREATE TABLE IF NOT EXISTS `sampling_bottle_group_sequence` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `current_sequence` int NOT NULL DEFAULT 0 COMMENT '当前序号',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_sequence` (`sampling_task_id`),
  CONSTRAINT `fk_bottle_sequence_task` FOREIGN KEY (`sampling_task_id`) 
    REFERENCES `sampling_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='瓶组编号序列表';
