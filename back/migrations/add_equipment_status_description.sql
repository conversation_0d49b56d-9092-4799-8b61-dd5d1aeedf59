-- 为设备管理表添加设备状态说明字段
-- 执行时间：2025-01-XX
-- 说明：新增设备状态说明字段，当设备状态为停用时，此字段为必填项

-- 添加设备状态说明字段
ALTER TABLE equipment_management 
ADD COLUMN equipment_status_description TEXT COMMENT '设备状态说明';

-- 验证字段添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'equipment_management' 
    AND COLUMN_NAME = 'equipment_status_description';

-- 验证数据
SELECT 
    COUNT(*) as total_count,
    COUNT(equipment_status_description) as with_description,
    COUNT(*) - COUNT(equipment_status_description) as without_description
FROM equipment_management;
