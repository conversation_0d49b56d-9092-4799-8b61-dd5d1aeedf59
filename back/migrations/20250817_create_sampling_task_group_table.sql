-- 创建采样任务分组表
-- 执行时间: 2025-08-17
-- 描述: 创建新的采样任务分组表，用于替代原有的执行人指派表，实现分组管理和执行人指派功能

-- 创建采样任务分组表
CREATE TABLE IF NOT EXISTS `sampling_task_group` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `sampling_task_id` BIGINT NOT NULL COMMENT '采样任务ID',
    `cycle_number` INT NOT NULL COMMENT '周期序号',
    `cycle_type` VARCHAR(50) COMMENT '周期类型',
    `detection_category` VARCHAR(100) COMMENT '检测类别',
    `point_name` VARCHAR(200) COMMENT '点位名称',
    `cycle_item_ids` TEXT COMMENT '关联的周期条目ID列表（JSON格式）',
    `assigned_user_ids` TEXT COMMENT '分配的执行人ID列表（JSON格式）',
    `create_by` BIGINT COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` BIGINT COMMENT '更新人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_sampling_task_group_task_id` (`sampling_task_id`),
    INDEX `idx_sampling_task_group_cycle` (`cycle_number`, `cycle_type`, `detection_category`, `point_name`),
    
    -- 唯一约束：同一任务下的分组键唯一
    UNIQUE KEY `uk_task_group` (`sampling_task_id`, `cycle_number`, `cycle_type`, `detection_category`, `point_name`),
    
    -- 外键约束
    FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`create_by`) REFERENCES `sys_user`(`user_id`),
    FOREIGN KEY (`update_by`) REFERENCES `sys_user`(`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样任务分组表';

-- 数据迁移：将现有的执行人指派数据迁移到新的分组表
-- 注意：这个操作会将现有的 sampling_task_executor_assignment 表数据迁移到新表
INSERT INTO `sampling_task_group` (
    `sampling_task_id`,
    `cycle_number`,
    `cycle_type`,
    `detection_category`,
    `point_name`,
    `cycle_item_ids`,
    `assigned_user_ids`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`
)
SELECT 
    `sampling_task_id`,
    `cycle_number`,
    `cycle_type`,
    `detection_category`,
    `point_name`,
    `cycle_item_ids`,
    `assigned_user_ids`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`
FROM `sampling_task_executor_assignment`
WHERE EXISTS (SELECT 1 FROM `sampling_task_executor_assignment`);

-- 备份原表（可选）
-- RENAME TABLE `sampling_task_executor_assignment` TO `sampling_task_executor_assignment_backup`;

-- 或者直接删除原表（谨慎操作）
-- DROP TABLE IF EXISTS `sampling_task_executor_assignment`;
