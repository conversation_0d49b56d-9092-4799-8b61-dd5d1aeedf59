-- 添加类别代码字段到技术手册价格表
-- 用于支持技术手册价格与类别的关联

-- 添加 category_code 字段
ALTER TABLE technical_manual_price 
ADD COLUMN category_code VARCHAR(100) COMMENT '类别代码，关联技术手册类别表';

-- 添加索引以提高查询性能
CREATE INDEX idx_technical_manual_price_category_code ON technical_manual_price(category_code);

-- 添加外键约束（可选，根据需要启用）
-- ALTER TABLE technical_manual_price 
-- ADD CONSTRAINT fk_technical_manual_price_category 
-- FOREIGN KEY (category_code) REFERENCES technical_manual_category(category_code);

-- 验证字段添加成功
SELECT COUNT(*) as total_records
FROM technical_manual_price;
