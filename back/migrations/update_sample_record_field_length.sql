-- 更新样品记录表字段长度
-- 执行时间：2025-08-17
-- 说明：增加检测参数和检测方法字段的长度限制，以支持合并多个检测项目的参数和方法

-- 更新检测参数字段长度：从varchar(100)增加到text
ALTER TABLE `sample_record` 
MODIFY COLUMN `detection_parameter` TEXT COMMENT '检测参数';

-- 更新检测方法字段长度：从varchar(200)增加到text  
ALTER TABLE `sample_record` 
MODIFY COLUMN `detection_method` TEXT COMMENT '检测方法';

-- 验证字段修改成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'sample_record'
    AND COLUMN_NAME IN ('detection_parameter', 'detection_method')
ORDER BY ORDINAL_POSITION;
