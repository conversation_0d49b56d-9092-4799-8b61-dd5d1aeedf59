-- 创建设备管理表
CREATE TABLE IF NOT EXISTS `equipment_management` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `equipment_number` varchar(100) NOT NULL COMMENT '设备编号（唯一值）',
  `equipment_name` varchar(200) NOT NULL COMMENT '测量设备（器具）名称',
  `enable_date` date DEFAULT NULL COMMENT '启用日期',
  `equipment_type` varchar(100) DEFAULT NULL COMMENT '设备类型',
  `model_specification` varchar(200) DEFAULT NULL COMMENT '型号规格',
  `factory_number` varchar(100) DEFAULT NULL COMMENT '出厂编号',
  `manufacturer` varchar(200) DEFAULT NULL COMMENT '制造商',
  `indicator_characteristics` text COMMENT '指标特性',
  `calibration_institution` varchar(200) DEFAULT NULL COMMENT '检定/校准机构',
  `traceability_method` varchar(100) DEFAULT NULL COMMENT '溯源方式',
  `current_calibration_date` date DEFAULT NULL COMMENT '本次校准/核查日期',
  `certificate_number` varchar(100) DEFAULT NULL COMMENT '证书编号',
  `next_calibration_date` date DEFAULT NULL COMMENT '下次校准/核查日期',
  `interval_days` varchar(20) DEFAULT NULL COMMENT '间隔日期（天数）',
  `interim_check_date` date DEFAULT NULL COMMENT '期间核查日期',
  `manager` varchar(100) DEFAULT NULL COMMENT '管理者',
  `equipment_status` varchar(50) DEFAULT NULL COMMENT '设备状态',
  `location` varchar(200) DEFAULT NULL COMMENT '放置地点',
  `calibration_remark` text COMMENT '备注（校准确认）',
  `amount` decimal(15,2) DEFAULT NULL COMMENT '金额',
  `contract` varchar(200) DEFAULT NULL COMMENT '合同',
  `invoice` varchar(200) DEFAULT NULL COMMENT '发票',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_equipment_number` (`equipment_number`),
  KEY `idx_equipment_name` (`equipment_name`),
  KEY `idx_equipment_type` (`equipment_type`),
  KEY `idx_equipment_status` (`equipment_status`),
  KEY `idx_manager` (`manager`),
  KEY `idx_enable_date` (`enable_date`),
  KEY `idx_next_calibration_date` (`next_calibration_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备管理表';
