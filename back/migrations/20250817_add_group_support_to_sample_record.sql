-- 为样品记录表添加任务分组支持
-- 执行时间: 2025-08-17

-- 1. 添加任务分组ID字段
ALTER TABLE sample_record 
ADD COLUMN sampling_task_group_id BIGINT COMMENT '采样任务分组ID';

-- 2. 添加外键约束
ALTER TABLE sample_record 
ADD CONSTRAINT fk_sample_record_group_id 
FOREIGN KEY (sampling_task_group_id) REFERENCES sampling_task_group(id);

-- 3. 修改原有的执行任务指派ID字段为可空（为了兼容性）
ALTER TABLE sample_record 
MODIFY COLUMN sampling_task_assignment_id BIGINT NULL COMMENT '采样任务执行指派ID（兼容字段）';

-- 4. 添加索引以提高查询性能
CREATE INDEX idx_sample_record_group_id ON sample_record(sampling_task_group_id);
CREATE INDEX idx_sample_record_group_cycle ON sample_record(sampling_task_group_id, cycle_number);

-- 5. 添加检查约束，确保至少有一个关联ID不为空
ALTER TABLE sample_record 
ADD CONSTRAINT chk_sample_record_association 
CHECK (sampling_task_assignment_id IS NOT NULL OR sampling_task_group_id IS NOT NULL);

-- 注意：
-- 1. 现有数据仍然使用 sampling_task_assignment_id
-- 2. 新的分组系统将使用 sampling_task_group_id
-- 3. 两个字段可以同时存在，但至少要有一个不为空
-- 4. 后续可以逐步迁移数据并最终移除 sampling_task_assignment_id 字段
