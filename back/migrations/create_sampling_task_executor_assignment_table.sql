-- 创建采样任务执行人指派表
CREATE TABLE IF NOT EXISTS `sampling_task_executor_assignment` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `sampling_task_id` BIGINT NOT NULL COMMENT '采样任务ID',
    `cycle_number` INT NOT NULL COMMENT '周期序号',
    `cycle_type` VARCHAR(50) COMMENT '周期类型',
    `detection_category` VARCHAR(100) COMMENT '检测类别',
    `point_name` VARCHAR(200) COMMENT '点位名称',
    `assigned_user_ids` TEXT COMMENT '分配的执行人ID列表（JSON格式）',
    `create_by` BIGINT COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` BIGINT COMMENT '更新人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_sampling_task_executor_assignment_task_id` (`sampling_task_id`),
    INDEX `idx_sampling_task_executor_assignment_cycle` (`cycle_number`, `cycle_type`, `detection_category`, `point_name`),
    FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`create_by`) REFERENCES `sys_user`(`user_id`),
    FOREIGN KEY (`update_by`) REFERENCES `sys_user`(`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采样任务执行人指派表';
