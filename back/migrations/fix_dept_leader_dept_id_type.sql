-- 修复部门负责人表中dept_id字段的数据类型不匹配问题
-- 将dept_id从BIGINT改为INT以匹配sys_dept表的dept_id类型

USE lims;

-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 修改sys_dept_leader表中dept_id字段的数据类型
ALTER TABLE sys_dept_leader 
MODIFY COLUMN dept_id INT NOT NULL COMMENT '部门ID';

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证外键约束是否正常工作
-- 可以通过以下查询检查外键约束
-- SELECT 
--     TABLE_NAME,
--     COLUMN_NAME,
--     CONSTRAINT_NAME,
--     REFERENCED_TABLE_NAME,
--     REFERENCED_COLUMN_NAME
-- FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
-- WHERE REFERENCED_TABLE_NAME = 'sys_dept'
--   AND TABLE_NAME = 'sys_dept_leader';