-- 为技术手册表添加"是否有资质"字段
-- 支持批量导入功能

-- 添加"是否有资质"字段
ALTER TABLE technical_manual ADD COLUMN has_qualification VARCHAR(1) DEFAULT '0' COMMENT '是否有资质(0=有，1=无)' AFTER qualification_date;
-- 为新字段添加索引
CREATE INDEX idx_technical_manual_has_qualification ON technical_manual(has_qualification);

-- 更新表注释
ALTER TABLE technical_manual COMMENT = '技术手册表 - 存储检测技术手册信息，包括资质状态';

-- 插入示例数据（可选）
-- UPDATE technical_manual SET has_qualification = '0' WHERE qualification_code IS NOT NULL;
-- UPDATE technical_manual SET has_qualification = '1' WHERE qualification_code IS NULL;
