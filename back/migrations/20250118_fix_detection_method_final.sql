-- 修复检测方法字段长度问题
-- 执行时间: 2025-01-18

-- 1. 删除采样瓶组表的检测方法索引
ALTER TABLE `sampling_bottle_group` DROP INDEX `idx_detection_method`;

-- 2. 修改采样瓶组表的检测方法字段长度为TEXT
ALTER TABLE `sampling_bottle_group` 
MODIFY COLUMN `detection_method` TEXT COMMENT '检测方法';

-- 3. 重新创建索引（TEXT字段需要指定长度）
ALTER TABLE `sampling_bottle_group` 
ADD INDEX `idx_detection_method` (`detection_method`(255));

-- 4. 修改瓶组与检测方法关联表（如果存在索引的话）
-- 先检查是否存在表
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'bottle_maintenance_method');

-- 如果表存在，则修改字段
SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE `bottle_maintenance_method` MODIFY COLUMN `detection_method` TEXT COMMENT ''检测方法''', 
    'SELECT ''Table bottle_maintenance_method does not exist'' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
