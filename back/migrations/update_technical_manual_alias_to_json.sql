-- 更新技术手册表的别名字段为JSON格式
-- 执行时间：2024-01-XX

-- 1. 备份现有数据
CREATE TABLE technical_manual_alias_backup AS 
SELECT id, common_alias 
FROM technical_manual 
WHERE common_alias IS NOT NULL AND common_alias != '';

-- 2. 添加新的JSON字段
ALTER TABLE technical_manual 
ADD COLUMN alias_list JSON COMMENT '别名列表（JSON格式）' AFTER common_alias;

-- 3. 迁移现有别名数据到JSON格式
UPDATE technical_manual 
SET alias_list = JSON_ARRAY(common_alias) 
WHERE common_alias IS NOT NULL AND common_alias != '';

-- 4. 处理逗号分隔的别名（如果存在）
UPDATE technical_manual 
SET alias_list = (
    SELECT JSON_ARRAYAGG(TRIM(alias))
    FROM (
        SELECT TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(common_alias, ',', numbers.n), ',', -1)) as alias
        FROM (
            SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
            UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
        ) numbers
        WHERE CHAR_LENGTH(common_alias) - CHAR_LENGTH(REPLACE(common_alias, ',', '')) >= numbers.n - 1
        AND TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(common_alias, ',', numbers.n), ',', -1)) != ''
    ) aliases
)
WHERE common_alias IS NOT NULL 
AND common_alias != '' 
AND common_alias LIKE '%,%';

-- 5. 验证数据迁移
SELECT 
    id,
    common_alias as old_alias,
    alias_list as new_alias_json,
    JSON_LENGTH(alias_list) as alias_count
FROM technical_manual 
WHERE alias_list IS NOT NULL
LIMIT 10;

-- 6. 创建JSON搜索的函数索引（MySQL 8.0+）
-- ALTER TABLE technical_manual 
-- ADD INDEX idx_alias_search ((CAST(alias_list AS CHAR(500) ARRAY)));

-- 注意：执行完成后，可以考虑删除 common_alias 字段
-- ALTER TABLE technical_manual DROP COLUMN common_alias;
