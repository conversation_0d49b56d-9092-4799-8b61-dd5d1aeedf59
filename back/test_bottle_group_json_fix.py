#!/usr/bin/env python3
"""
测试瓶组JSON字段解析修复
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_bottle_group_json_parsing():
    """测试瓶组JSON字段解析修复"""
    
    print("🔧 测试瓶组JSON字段解析修复...")
    
    # 测试获取样品14的瓶组列表（之前报错的接口）
    sample_id = 14
    
    print(f"\n1. 测试获取样品 {sample_id} 的瓶组列表:")
    
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('code') == 200:
                bottle_groups = data.get('data', [])
                print(f"\n   ✅ 瓶组列表获取成功:")
                print(f"     瓶组数量: {len(bottle_groups)}")
                
                if bottle_groups:
                    for i, group in enumerate(bottle_groups, 1):
                        print(f"\n     瓶组{i}:")
                        print(f"       ID: {group.get('id')}")
                        print(f"       瓶组编号: {group.get('bottleGroupCode')}")
                        print(f"       瓶组类型: {group.get('bottleType')}")
                        print(f"       容器容量: {group.get('bottleVolume')}")
                        print(f"       存储方式: {group.get('storageStyles')}")
                        print(f"       固定方式: {group.get('fixStyles')}")
                        print(f"       检测方法: {group.get('detectionMethod')}")
                        print(f"       样品数量: {group.get('sampleCount')}")
                        print(f"       状态: {group.get('status')}")
                        
                        # 验证JSON字段类型
                        storage_styles = group.get('storageStyles')
                        fix_styles = group.get('fixStyles')
                        
                        if isinstance(storage_styles, list):
                            print(f"       ✅ storageStyles是列表类型: {storage_styles}")
                        else:
                            print(f"       ❌ storageStyles不是列表类型: {type(storage_styles)} - {storage_styles}")
                        
                        if isinstance(fix_styles, list):
                            print(f"       ✅ fixStyles是列表类型: {fix_styles}")
                        else:
                            print(f"       ❌ fixStyles不是列表类型: {type(fix_styles)} - {fix_styles}")
                else:
                    print(f"     ⚠️  样品 {sample_id} 没有关联的瓶组")
                
                return True
            else:
                print(f"   ❌ API调用失败:")
                print(f"     错误码: {data.get('code')}")
                print(f"     错误消息: {data.get('msg')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def test_task_bottle_groups():
    """测试任务瓶组列表"""
    
    print(f"\n2. 测试任务瓶组列表:")
    
    # 使用最新创建的任务ID 27
    task_id = 27
    
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   任务ID: {task_id}")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('code') == 200:
                bottle_groups = data.get('data', [])
                print(f"   ✅ 任务瓶组列表获取成功:")
                print(f"     瓶组数量: {len(bottle_groups)}")
                
                if bottle_groups:
                    for i, group in enumerate(bottle_groups, 1):
                        print(f"\n     瓶组{i}:")
                        print(f"       ID: {group.get('id')}")
                        print(f"       瓶组编号: {group.get('bottleGroupCode')}")
                        print(f"       瓶组类型: {group.get('bottleType')}")
                        print(f"       存储方式: {group.get('storageStyles')} (类型: {type(group.get('storageStyles'))})")
                        print(f"       固定方式: {group.get('fixStyles')} (类型: {type(group.get('fixStyles'))})")
                        print(f"       样品数量: {group.get('sampleCount')}")
                        
                        # 验证JSON字段类型
                        storage_styles = group.get('storageStyles')
                        fix_styles = group.get('fixStyles')
                        
                        if isinstance(storage_styles, list):
                            print(f"       ✅ storageStyles类型正确")
                        else:
                            print(f"       ❌ storageStyles类型错误: {type(storage_styles)}")
                        
                        if isinstance(fix_styles, list):
                            print(f"       ✅ fixStyles类型正确")
                        else:
                            print(f"       ❌ fixStyles类型错误: {type(fix_styles)}")
                else:
                    print(f"     ⚠️  任务 {task_id} 没有瓶组")
                
                return True
            else:
                print(f"   ❌ API调用失败: {data.get('msg')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def print_fix_summary():
    """打印修复总结"""
    
    print(f"\n🚀 瓶组JSON字段解析修复完成！")
    print(f"\n📋 修复详情:")
    
    print(f"\n   ❌ 修复前的问题:")
    print(f"     1. Pydantic验证错误: storage_styles和fix_styles字段期望列表类型")
    print(f"     2. 数据库返回的是JSON字符串，如: '[\"密封\"]'")
    print(f"     3. 直接传递给Pydantic模型导致类型不匹配")
    print(f"     4. 接口返回500错误: Input should be a valid list")
    
    print(f"\n   ✅ 修复后的解决方案:")
    print(f"     1. 在服务层添加JSON字符串解析逻辑")
    print(f"     2. 检查字段类型，如果是字符串则使用json.loads()解析")
    print(f"     3. 如果已经是列表则直接使用")
    print(f"     4. 解析失败时使用空列表作为默认值")
    print(f"     5. 在DAO层和Service层都添加了解析逻辑")
    
    print(f"\n🔧 修复的文件:")
    print(f"   1. back/module_sampling/service/sampling_bottle_group_service.py")
    print(f"      - get_bottle_groups_by_sample方法中添加JSON解析")
    print(f"   2. back/module_sampling/dao/sampling_bottle_group_dao.py")
    print(f"      - get_bottle_groups_with_details_by_task_id方法中添加JSON解析")
    
    print(f"\n🎯 修复效果:")
    print(f"   1. storage_styles和fix_styles字段正确解析为列表类型")
    print(f"   2. 瓶组列表接口正常返回数据")
    print(f"   3. 前端可以正常显示瓶组信息")
    print(f"   4. 消除了Pydantic验证错误")

if __name__ == "__main__":
    success1 = test_bottle_group_json_parsing()
    success2 = test_task_bottle_groups()
    print_fix_summary()
    
    if success1 and success2:
        print("\n🎯 瓶组JSON字段解析修复测试通过！功能正常工作！")
    else:
        print("\n💥 瓶组JSON字段解析修复测试失败，请检查日志获取详细信息")
