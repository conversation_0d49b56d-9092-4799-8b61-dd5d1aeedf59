#!/usr/bin/env python3
"""
测试瓶组API调用
"""

import requests
import json

BASE_URL = "http://127.0.0.1:9099"
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

def test_bottle_group_apis():
    """测试瓶组相关的API"""
    
    print("🧪 测试瓶组相关API...")
    
    # 1. 测试获取样品关联的瓶组列表
    sample_id = 12  # 从数据库查询结果中得到的样品ID
    print(f"\n1. 测试获取样品 {sample_id} 关联的瓶组列表:")
    
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/sample/{sample_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应成功: {data.get('msg', '')}")
            
            bottle_groups = data.get('data', [])
            print(f"   瓶组数量: {len(bottle_groups)}")
            
            if bottle_groups:
                print("   前3个瓶组:")
                for i, bottle in enumerate(bottle_groups[:3], 1):
                    print(f"     {i}. {bottle.get('bottleGroupCode', 'N/A')} (状态: {bottle.get('status', 'N/A')})")
                    print(f"        类型: {bottle.get('bottleType', '默认瓶组')}")
                    print(f"        容量: {bottle.get('bottleVolume', '-')}")
            else:
                print("   ❌ 没有返回瓶组数据")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 2. 测试获取任务的瓶组列表
    task_id = 18  # 从数据库查询结果中得到的任务ID
    print(f"\n2. 测试获取任务 {task_id} 的瓶组列表:")
    
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/bottle-groups/task/{task_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应成功: {data.get('msg', '')}")
            
            bottle_groups = data.get('data', [])
            print(f"   瓶组数量: {len(bottle_groups)}")
            
            if bottle_groups:
                print("   前3个瓶组:")
                for i, bottle in enumerate(bottle_groups[:3], 1):
                    print(f"     {i}. {bottle.get('bottleGroupCode', 'N/A')} (状态: {bottle.get('status', 'N/A')})")
                    print(f"        类型: {bottle.get('bottleType', '默认瓶组')}")
                    print(f"        容量: {bottle.get('bottleVolume', '-')}")
            else:
                print("   ❌ 没有返回瓶组数据")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 3. 测试获取分组的样品记录
    group_id = 27  # 从数据库查询结果中得到的分组ID
    print(f"\n3. 测试获取分组 {group_id} 的样品记录:")
    
    try:
        response = requests.get(
            f"{BASE_URL}/sampling/sample-records/group/{group_id}",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应成功: {data.get('msg', '')}")
            
            samples = data.get('data', [])
            print(f"   样品数量: {len(samples)}")
            
            if samples:
                for i, sample in enumerate(samples, 1):
                    print(f"     {i}. 样品 {sample.get('sampleNumber', 'N/A')} (ID: {sample.get('id', 'N/A')})")
                    print(f"        点位: {sample.get('pointName', '-')}")
                    print(f"        周期: {sample.get('cycleNumber', '-')}")
            else:
                print("   ❌ 没有返回样品数据")
        else:
            print(f"   ❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    print(f"\n✅ API测试完成")

if __name__ == "__main__":
    test_bottle_group_apis()
