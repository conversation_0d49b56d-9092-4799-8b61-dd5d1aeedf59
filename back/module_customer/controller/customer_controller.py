from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.get_db import get_db
from utils.response_util import ResponseUtil
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.login_service import LoginService
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_customer.entity.vo.customer_vo import (
    AddCustomerModel,
    CustomerModel,
    CustomerPageQueryModel,
    CustomerQueryModel,
    EditCustomerModel,
)
from module_customer.service.customer_service import CustomerService
from utils.page_util import PageResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel

# 创建路由
router = APIRouter(prefix='/customer', tags=['客户管理'])


@router.get('/list', response_model=List[CustomerModel], summary='获取客户列表')
async def get_customer_list(
    request: Request,
    query_params: CustomerQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    获取客户列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户列表
    """
    customer_list = await CustomerService.get_customer_list_services(db, query_params)
    return ResponseUtil.success(data=customer_list)


@router.get('/tree', summary='获取客户树形结构')
async def get_customer_tree(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    获取客户树形结构（集团及其下级公司）

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户树形结构
    """
    tree_data = await CustomerService.get_customer_tree_services(db)
    return ResponseUtil.success(data=tree_data)


@router.get('/page', response_model=PageResponseModel, summary='获取客户分页列表')
async def get_customer_page(
    request: Request,
    query_params: CustomerPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    获取客户分页列表

    :param request: 请求对象
    :param query_params: 分页查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户分页列表
    """
    customer_page = await CustomerService.get_customer_page_list_services(db, query_params)
    return ResponseUtil.success(data=customer_page)


@router.get('/dept-customers/page', response_model=PageResponseModel, summary='获取部门客户分页列表（下属员工负责的客户）')
async def get_dept_customers_page(
    request: Request,
    query_params: CustomerPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取部门客户分页列表（当前用户下属员工负责的客户）

    :param request: 请求对象
    :param query_params: 分页查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 部门客户分页列表
    """
    customer_page = await CustomerService.get_all_customers_page_services(db, query_params, current_user)
    return ResponseUtil.success(data=customer_page)


@router.get('/all-customers/page', response_model=PageResponseModel, summary='获取所有客户分页列表（系统中所有客户）', dependencies=[Depends(CheckUserInterfaceAuth('customer:all'))])
async def get_all_customers_page(
    request: Request,
    query_params: CustomerPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取所有客户分页列表（系统中所有客户，需要customer:all权限）

    :param request: 请求对象
    :param query_params: 分页查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 所有客户分页列表
    """
    customer_page = await CustomerService.get_all_customers_page_services_with_permission(db, query_params)
    return ResponseUtil.success(data=customer_page)


@router.get('/my-customers/page', response_model=PageResponseModel, summary='获取我的客户分页列表')
async def get_my_customers_page(
    request: Request,
    query_params: CustomerPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取我的客户分页列表（当前用户负责的客户）

    :param request: 请求对象
    :param query_params: 分页查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 我的客户分页列表
    """
    customer_page = await CustomerService.get_my_customers_page_services(db, query_params, current_user)
    return ResponseUtil.success(data=customer_page)


@router.get('/check-dept-leader', summary='检查当前用户是否为部门负责人')
async def check_dept_leader(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    检查当前用户是否为部门负责人

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 是否为部门负责人
    """
    result = await CustomerService.check_user_is_dept_leader_services(db, current_user)
    return ResponseUtil.success(data=result)


@router.get('/check/{customer_name}', summary='校验客户名称是否唯一')
async def check_customer_name_unique(
    request: Request,
    customer_name: str,
    customer_id: int = None,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    校验客户名称是否唯一

    :param request: 请求对象
    :param customer_name: 客户名称
    :param customer_id: 客户ID（编辑时使用）
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 是否唯一
    """
    result = await CustomerService.check_customer_name_unique_services(db, customer_name, customer_id)
    return ResponseUtil.success(data=result)


@router.get('/{customer_id}', response_model=CustomerModel, summary='获取客户详情')
async def get_customer_detail(
    request: Request,
    customer_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    获取客户详情

    :param request: 请求对象
    :param customer_id: 客户ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户详情
    """
    customer = await CustomerService.get_customer_detail_services(db, customer_id)
    return ResponseUtil.success(data=customer)


@router.post('', response_model=CustomerModel, summary='新增客户')
async def add_customer(
    request: Request,
    customer: AddCustomerModel,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    新增客户

    :param request: 请求对象
    :param customer: 新增客户对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    result = await CustomerService.add_customer_services(db, request, customer, current_user)
    return ResponseUtil.success(data=result)


@router.put('', response_model=CustomerModel, summary='编辑客户')
async def edit_customer(
    request: Request,
    customer: EditCustomerModel,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    编辑客户

    :param request: 请求对象
    :param customer: 编辑客户对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    result = await CustomerService.edit_customer_services(db, request, customer, current_user)
    return ResponseUtil.success(data=result)


@router.delete('/{customer_id}', summary='删除客户')
async def delete_customer(
    request: Request,
    customer_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    删除客户

    :param request: 请求对象
    :param customer_id: 客户ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    result = await CustomerService.delete_customer_services(db, request, customer_id, current_user)
    return ResponseUtil.success(data=result)

