"""
瓶组编码序列表数据模型
"""
from sqlalchemy import Column, Integer, DateTime

from config.database import Base


class BottleCodeSequence(Base):
    """
    瓶组编码序列表
    """
    __tablename__ = 'bottle_code_sequence'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    current_value = Column(Integer, nullable=False, default=0, comment='当前序列值')
    update_time = Column(DateTime, nullable=True, comment='更新时间')
