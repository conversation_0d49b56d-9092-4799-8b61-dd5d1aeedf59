"""
瓶组管理与技术手册关联表数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, BigInteger

from config.database import Base


class BottleMaintenanceTechnicalManual(Base):
    """
    瓶组管理与技术手册关联表
    """

    __tablename__ = "bottle_maintenance_technical_manual"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    bottle_maintenance_id = Column(Integer, ForeignKey("bottle_maintenance.id"), nullable=False, comment="瓶组管理ID")
    technical_manual_id = Column(BigInteger, ForeignKey("technical_manual.id"), nullable=False, comment="技术手册ID")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
