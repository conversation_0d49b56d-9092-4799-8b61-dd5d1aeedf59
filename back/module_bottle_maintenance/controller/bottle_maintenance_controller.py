"""
瓶组管理控制器
"""

import io

import pandas as pd
from fastapi import APIRouter, Depends, Request, Query, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_bottle_maintenance.entity.vo.bottle_maintenance_vo import (
    AddBottleMaintenanceModel,
    EditBottleMaintenanceModel,
    BottleMaintenanceImportModel,
    BottleMaintenancePageQueryModel,
)
from module_bottle_maintenance.service.bottle_maintenance_service import BottleMaintenanceService
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil

router = APIRouter(prefix="/bottle-maintenance", tags=["瓶组管理"])


@router.get("/list", response_model=PageResponseModel, summary="获取瓶组管理列表")
async def get_bottle_maintenance_list(
    request: Request,
    query_params: BottleMaintenancePageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取瓶组管理列表

    :param bottle_code: 瓶组编码
    :param bottle_type: 容器类型
    :param bottle_volume: 容器容量
    :param storage_styles: 存储方式
    :param fix_styles: 固定方式
    :param sample_age_unit: 样品时效单位
    :param parameter: 技术手册参数
    :param method: 技术手册方法
    :param category: 技术手册类别
    :param page_num: 页码
    :param page_size: 每页数量
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 瓶组管理列表
    """

    service = BottleMaintenanceService(db)
    page_result = await service.get_bottle_maintenance_page(query_params)
    return ResponseUtil.success(data=page_result)


@router.get("/{bottle_maintenance_id}", response_model=CrudResponseModel, summary="获取瓶组管理详情")
async def get_bottle_maintenance_detail(
    bottle_maintenance_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取瓶组管理详情

    :param bottle_maintenance_id: 瓶组管理ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 瓶组管理详情
    """
    service = BottleMaintenanceService(db)
    result = await service.get_bottle_maintenance_by_id(bottle_maintenance_id)

    return ResponseUtil.success(data=result)


@router.post("/add", response_model=CrudResponseModel, summary="添加瓶组管理")
async def add_bottle_maintenance(
    add_model: AddBottleMaintenanceModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    添加瓶组管理

    :param add_model: 添加模型
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 添加结果
    """
    service = BottleMaintenanceService(db)
    result = await service.add_bottle_maintenance(add_model, current_user)

    return ResponseUtil.success(data=result, msg="添加成功")


@router.put("/edit", response_model=CrudResponseModel, summary="编辑瓶组管理")
async def edit_bottle_maintenance(
    edit_model: EditBottleMaintenanceModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑瓶组管理

    :param edit_model: 编辑模型
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = BottleMaintenanceService(db)
    result = await service.edit_bottle_maintenance(edit_model, current_user)

    return ResponseUtil.success(data=result, msg="编辑成功")


@router.delete("/delete", response_model=CrudResponseModel, summary="删除瓶组管理")
async def delete_bottle_maintenance(
    bottle_maintenance_id: int = Query(..., description="瓶组管理ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除瓶组管理

    :param bottle_maintenance_id: 瓶组管理ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = BottleMaintenanceService(db)
    await service.delete_bottle_maintenance(bottle_maintenance_id, current_user)

    return ResponseUtil.success(msg="删除成功")


@router.post("/batch-import", response_model=CrudResponseModel, summary="批量导入瓶组管理")
async def batch_import_bottle_maintenance(
    file: UploadFile = File(..., description="Excel文件"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量导入瓶组管理

    :param file: Excel文件
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 导入结果
    """
    try:
        # 读取Excel文件
        content = await file.read()
        df = pd.read_excel(io.BytesIO(content))

        # 转换为导入模型
        import_data = []
        for _, row in df.iterrows():
            import_model = BottleMaintenanceImportModel(
                bottle_type=row.get("容器类型", ""),
                bottle_volume=row.get("容器容量", ""),
                storage_styles=row.get("存储方式", ""),
                fix_styles=row.get("固定方式", ""),
                sample_age=row.get("样品时效", None),
                sample_age_unit=row.get("样品时效单位", ""),
                remark=row.get("备注", ""),
                category=row.get("检测类别", ""),
                parameter=row.get("检测参数", ""),
                method=row.get("检测方法", ""),
            )
            import_data.append(import_model)

        # 执行批量导入
        service = BottleMaintenanceService(db)
        result = await service.batch_import_bottle_maintenance(import_data, current_user)

        return ResponseUtil.success(data=result, msg="导入完成")

    except Exception as e:
        return ResponseUtil.error(msg=f"导入失败: {str(e)}")


@router.get("/template/download", summary="下载导入模板")
async def download_import_template():
    """
    下载导入模板

    :return: Excel模板文件
    """
    from fastapi.responses import StreamingResponse

    # 创建模板数据
    template_data = {
        "容器类型": ["玻璃瓶", "木盒"],
        "容器容量": ["100ML", "1kg"],
        "存储方式": ["冷藏,避光", "常温"],
        "固定方式": ["尽快安装", "吊顶上"],
        "样品时效": [1, 2],
        "样品时效单位": ["小时", "天"],
        "备注": ["备注1", "备注2"],
        "检测类别": ["水质,土壤", "大气"],
        "检测参数": ["pH值,溶解氧", "温度"],
        "检测方法": ["玻璃电极法", "温度计法"],
    }

    df = pd.DataFrame(template_data)

    # 创建Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name="瓶组管理_导入模板", index=False)

    output.seek(0)

    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": "attachment; filename=bottle_maintenance_template.xlsx"},
    )
