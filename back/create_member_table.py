#!/usr/bin/env python3
"""
创建采样任务组员表的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import async_engine
from module_sampling.entity.do.sampling_task_member_do import SamplingTaskMember


async def create_table():
    """创建采样任务组员表"""
    try:
        # 导入Base类以确保所有表都被注册
        from config.database import Base
        
        # 创建表
        async with async_engine.begin() as conn:
            # 只创建SamplingTaskMember表
            await conn.run_sync(SamplingTaskMember.metadata.create_all)
            print("采样任务组员表创建成功！")
            
    except Exception as e:
        print(f"创建表失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    result = asyncio.run(create_table())
    if result:
        print("表创建完成")
    else:
        print("表创建失败")
        sys.exit(1)
