"""
合同管理控制器
"""

from typing import List

from fastapi import APIRouter, Query
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_contract.entity.vo.contract_business_vo import ContractBusinessInfoModel
from module_contract.entity.vo.contract_vo import (
    AddContractModel,
    EditContractModel,
    ContractQueryModel,
)
from module_contract.service.contract_business_service import ContractBusinessService
from module_contract.service.contract_service import ContractService
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil

contractController = APIRouter(prefix="/contract", tags=["合同管理"])


@contractController.get("/list", response_model=PageResponseModel)
async def get_contract_list(
    query_model: ContractQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """获取合同列表"""
    contract_service = ContractService(query_db)
    result = await contract_service.get_contract_list(query_model)

    return ResponseUtil.success(data=result)


@contractController.get("/my", response_model=PageResponseModel)
async def get_contract_list(
    query_model: ContractQueryModel = Depends(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """获取我的合同列表：当前登录账户是合同项目负责人/客服的，展示在“我的合同”"""
    contract_service = ContractService(query_db)
    query_model.project_manager = current_user.user.user_name
    query_model.project_service = current_user.user.user_name
    result = await contract_service.get_contract_list(query_model)

    return ResponseUtil.success(data=result)


@contractController.get("/{contract_id}", response_model=CrudResponseModel)
async def get_contract_detail(
    contract_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """获取合同详情"""
    contract_service = ContractService(query_db)
    result = await contract_service.get_contract_detail(contract_id)

    return ResponseUtil.success(data=result)


@contractController.post("/add", response_model=CrudResponseModel)
async def add_contract(
    contract_data: AddContractModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """新增合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.add_contract(contract_data, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])


@contractController.put("/update", response_model=CrudResponseModel)
async def update_contract(
    contract_data: EditContractModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """更新合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.update_contract(contract_data, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])


@contractController.delete("/{contract_id}", response_model=CrudResponseModel)
async def delete_contract(
    contract_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """删除合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.delete_contract(contract_id, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])


@contractController.delete("/batch", response_model=CrudResponseModel)
async def batch_delete_contracts(
    contract_ids: List[int],
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """批量删除合同"""
    contract_service = ContractService(query_db)
    result = await contract_service.batch_delete_contracts(contract_ids, current_user)

    return ResponseUtil.success(data=result, msg=result["message"])


"""
合同商务信息控制器
"""


@contractController.get("/contract_business/{contract_id}", response_model=CrudResponseModel)
async def get_contract_business(
    contract_id: int, db: AsyncSession = Depends(get_db), current_user=Depends(LoginService.get_current_user)
):
    """
    获取合同商务信息
    """
    try:
        contract_business_service = ContractBusinessService(db)
        business_info = await contract_business_service.get_contract_business(contract_id)

        return ResponseUtil.success(data=business_info)
    except Exception as e:
        return ResponseUtil.error(msg=str(e))


@contractController.post("/contract_business/save", response_model=CrudResponseModel)
async def save_contract_business(
    request: ContractBusinessInfoModel,
    db: AsyncSession = Depends(get_db),
    current_user=Depends(LoginService.get_current_user),
):
    """
    保存合同商务信息
    """
    try:
        contract_business_service = ContractBusinessService(db)
        business_info = await contract_business_service.save_contract_business(request, current_user)

        return ResponseUtil.success(data=business_info, msg="保存成功")
    except Exception as e:
        return ResponseUtil.error(msg=str(e))
