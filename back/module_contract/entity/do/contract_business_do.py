"""
合同商务信息数据模型
"""

from sqlalchemy import Column, Integer, String, Numeric, DateTime, ForeignKey
from config.database import Base


class ContractBusinessDepartment(Base):
    """
    合同商务信息-部门分摊表
    """

    __tablename__ = "contract_business_department"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    project_code = Column(String(100), nullable=False, comment="项目报价编号")
    dept_id = Column(Integer, nullable=False, comment="部门ID")
    allocation_amount = Column(Numeric(15, 2), nullable=False, comment="分摊金额")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    del_flag = Column(String(1), nullable=True, default="0", comment="删除标志(0代表存在 1代表删除)")


class ContractBusinessTask(Base):
    """
    合同商务信息-任务拆解表
    """

    __tablename__ = "contract_business_task"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    contract_id = Column(Integer, ForeignKey("contract.id"), nullable=False, comment="合同ID")
    project_code = Column(String(100), nullable=False, comment="项目报价编号")
    task_code = Column(String(100), nullable=False, comment="任务编号")
    task_amount = Column(Numeric(15, 2), nullable=False, comment="任务金额")

    # 通用字段
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")
    del_flag = Column(String(1), nullable=True, default="0", comment="删除标志(0代表存在 1代表删除)")
