#!/usr/bin/env python3
"""
测试分组编号生成功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from config.database import Base
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
from module_sampling.entity.do.sampling_task_do import SamplingTask


async def test_group_code_generation():
    """测试分组编号生成功能"""
    
    # 创建数据库连接
    DATABASE_URL = "mysql+aiomysql://lims-user:lims-Root1@127.0.0.1:3306/lims"
    
    engine = create_async_engine(
        DATABASE_URL,
        echo=True,
        pool_pre_ping=True,
        pool_recycle=3600
    )
    
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        try:
            # 1. 检查是否已添加group_code字段
            print("1. 检查group_code字段是否存在...")
            result = await session.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'lims' 
                AND TABLE_NAME = 'sampling_task_group' 
                AND COLUMN_NAME = 'group_code'
            """))
            
            if result.fetchone():
                print("✅ group_code字段已存在")
            else:
                print("❌ group_code字段不存在，需要运行迁移脚本")
                return
            
            # 2. 查询现有的任务分组数据
            print("\n2. 查询现有任务分组数据...")
            result = await session.execute(text("""
                SELECT stg.id, stg.group_code, st.task_code, st.task_name
                FROM sampling_task_group stg
                JOIN sampling_task st ON stg.sampling_task_id = st.id
                LIMIT 5
            """))
            
            groups = result.fetchall()
            if groups:
                print("现有分组数据:")
                for group in groups:
                    print(f"  分组ID: {group[0]}, 分组编号: {group[1]}, 任务编号: {group[2]}, 任务名称: {group[3]}")
            else:
                print("暂无分组数据")
            
            # 3. 测试分组编号格式
            print("\n3. 测试分组编号格式...")
            if groups:
                for group in groups:
                    group_id, group_code, task_code, task_name = group
                    expected_code = f"{task_code}-{group_id}"
                    
                    if group_code == expected_code:
                        print(f"✅ 分组 {group_id} 编号格式正确: {group_code}")
                    else:
                        print(f"❌ 分组 {group_id} 编号格式错误: 期望 {expected_code}, 实际 {group_code}")
            
            print("\n✅ 测试完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        finally:
            await engine.dispose()


if __name__ == "__main__":
    asyncio.run(test_group_code_generation())
