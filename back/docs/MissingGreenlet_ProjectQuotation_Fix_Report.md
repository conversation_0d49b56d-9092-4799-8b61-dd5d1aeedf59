# ProjectQuotation MissingGreenlet 错误修复报告

## 问题描述

在调用项目报价相关API时，出现了 `sqlalchemy.exc.MissingGreenlet` 错误：

```
(sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was <PERSON><PERSON> attempted in an unexpected place?
[SQL: SELECT project_quotation.id AS project_quotation_id, project_quotation.project_name AS project_quotation_project_name, ...
FROM project_quotation 
WHERE project_quotation.id = %s]
[parameters: [{'pk_1': 39}]]
```

## 错误原因分析

1. **根本原因**: SQLAlchemy在异步上下文中进行了同步的数据库操作，通常是由于关系属性的懒加载（lazy loading）导致的。

2. **具体场景**: 当通过 `get_by_id` 方法获取 `ProjectQuotation` 对象后，如果后续代码尝试访问该对象的关系属性（如 `items`、`attachments`、`other_fees` 等），SQLAlchemy会尝试进行懒加载，但在异步上下文中这会导致 `MissingGreenlet` 错误。

3. **ProjectQuotation模型的关系属性**:
   - `items`: 项目报价项目
   - `attachments`: 附件
   - `other_fees`: 其他费用
   - `total_fee`: 总费用
   - `approver_relations`: 审批关系
   - `customer_support_relations`: 客户支持关系
   - `detection_cycle_items`: 检测周期项目
   - `sampling_tasks`: 采样任务

## 修复方案

### 修改文件: `common/orm/async_orm_client.py`

在 `AsyncORMClient` 的 `get_by_id` 方法中，针对 `ProjectQuotation` 模型添加了 `selectinload` 预加载所有关系属性：

```python
from sqlalchemy.orm import selectinload
from module_quotation.entity.do.project_quotation_do import ProjectQuotation

async def get_by_id(self, id: int):
    stmt = select(self.model).where(self.model.id == id)
    
    # 针对ProjectQuotation模型，预加载所有关系以避免MissingGreenlet错误
    if hasattr(self.model, '__tablename__') and self.model.__tablename__ == 'project_quotation':
        stmt = stmt.options(
            selectinload(ProjectQuotation.items),
            selectinload(ProjectQuotation.attachments),
            selectinload(ProjectQuotation.other_fees),
            selectinload(ProjectQuotation.total_fee),
            selectinload(ProjectQuotation.approver_relations),
            selectinload(ProjectQuotation.customer_support_relations),
            selectinload(ProjectQuotation.detection_cycle_items),
            selectinload(ProjectQuotation.sampling_tasks)
        )
    
    result = await self.session.execute(stmt)
    return result.scalars().first()
```

## 修复原理

1. **预加载策略**: 使用 `selectinload` 在初始查询时就加载所有相关的关系数据，避免后续的懒加载。

2. **条件应用**: 只对 `ProjectQuotation` 模型应用这个预加载策略，不影响其他模型的性能。

3. **异步兼容**: `selectinload` 是异步兼容的，不会导致 `MissingGreenlet` 错误。

## 测试验证

创建了专门的测试文件 `tests/test_project_quotation_missing_greenlet_fix.py`，包含以下测试用例：

1. **test_project_quotation_get_by_id_no_missing_greenlet**: 测试 `get_by_id` 方法不会出现 MissingGreenlet 错误
2. **test_direct_project_quotation_query_no_missing_greenlet**: 测试直接查询项目报价不会出现 MissingGreenlet 错误
3. **test_project_quotation_with_relationships_no_missing_greenlet**: 测试访问项目报价关系属性不会出现 MissingGreenlet 错误

所有测试均通过，确认修复有效。

## 性能影响

1. **查询性能**: 由于预加载了所有关系数据，单次查询的数据量会增加，但避免了多次懒加载查询。
2. **内存使用**: 会增加一些内存使用，但对于项目报价这种通常需要完整信息的业务场景是合理的。
3. **网络开销**: 减少了数据库往返次数，总体上可能会提高性能。

## 相关修复历史

这是继之前修复 `SamplingTask` 相关的 MissingGreenlet 错误后的又一次类似修复。之前的修复记录在 `MissingGreenlet_Fix_Report.md` 中。

## 修复日期

2025-06-29

## 修复状态

✅ 已完成并通过测试验证