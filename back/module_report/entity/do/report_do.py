from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class Report(Base):
    """
    周报月报表
    """

    __tablename__ = 'report'

    report_id = Column(BigInteger, primary_key=True, autoincrement=True, comment='报告ID')
    report_type = Column(String(10), nullable=False, comment='报告类型（weekly/monthly）')
    report_date = Column(DateTime, nullable=False, comment='报告日期（周/月的第一天）')
    reporter_id = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=False, comment='报告人ID')
    summary = Column(Text, nullable=False, comment='本周/本月总结')
    plan = Column(Text, nullable=False, comment='下周/下月计划')
    problems = Column(Text, nullable=True, comment='本周/本月存在的问题')
    support_needed = Column(Text, nullable=True, comment='需要的支持')
    is_saturated = Column(String(1), nullable=True, comment='下周工作是否饱和（0不饱和 1饱和）仅周报')
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    del_flag = Column(String(1), default='0', comment='删除标志（0存在 2删除）')
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=True, comment='创建者')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=True, comment='更新者')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    remark = Column(String(500), default=None, comment='备注')

    # 关联关系
    reporter = relationship('SysUser', foreign_keys=[reporter_id])
    creator = relationship('SysUser', foreign_keys=[create_by])
    updater = relationship('SysUser', foreign_keys=[update_by])
