from fastapi import APIRouter, Depends, Request, Path
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.get_db import get_db
from utils.response_util import ResponseUtil
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.login_service import LoginService
from module_report.entity.vo.report_vo import (
    AddReportModel,
    ReportModel,
    ReportPageQueryModel,
    ReportQueryModel,
    EditReportModel,
)
from module_report.service.report_service import ReportService
from utils.page_util import PageResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel


# 创建路由
router = APIRouter(prefix='/report', tags=['周报月报管理'])


@router.get('/list', response_model=List[ReportModel], summary='获取周报月报列表')
async def get_report_list(
    request: Request,
    query_params: ReportQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取周报月报列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 周报月报列表
    """
    report_list = await ReportService.get_report_list_services(db, query_params, current_user)
    return ResponseUtil.success(data=report_list)


@router.get('/page', response_model=PageResponseModel, summary='分页获取周报月报列表')
async def get_report_page(
    request: Request,
    query_params: ReportPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    分页获取周报月报列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 周报月报分页列表
    """
    report_page_object = await ReportService.get_report_page_services(db, query_params, current_user)
    return ResponseUtil.success(data=report_page_object)


@router.get('/{report_id}', response_model=ReportModel, summary='获取周报月报详情')
async def get_report_detail(
    request: Request,
    report_id: int = Path(..., description="报告ID", gt=0),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取周报月报详情

    :param request: 请求对象
    :param report_id: 报告ID，必须是大于0的整数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 周报月报详情
    """
    report_detail = await ReportService.get_report_detail_services(db, report_id, current_user)
    return ResponseUtil.success(data=report_detail)


@router.post('', response_model=ReportModel, summary='新增周报月报')
async def add_report(
    request: Request,
    report: AddReportModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增周报月报

    :param request: 请求对象
    :param report: 新增周报月报对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    result = await ReportService.add_report_services(db, request, report, current_user)
    return ResponseUtil.success(data=result)


@router.put('', response_model=ReportModel, summary='编辑周报月报')
async def edit_report(
    request: Request,
    report: EditReportModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑周报月报

    :param request: 请求对象
    :param report: 编辑周报月报对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    result = await ReportService.edit_report_services(db, request, report, current_user)
    return ResponseUtil.success(data=result)


@router.delete('', response_model=CrudResponseModel, summary='批量删除周报月报')
async def delete_report(
    request: Request,
    report_ids: List[int],
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量删除周报月报

    :param request: 请求对象
    :param report_ids: 需要删除的周报月报ID列表
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    result = await ReportService.delete_report_services(db, report_ids, current_user)
    return ResponseUtil.success(data=result)
