#!/bin/bash
# 一键更新代码脚本
# 作用：拉取最新代码，更新前后端

set -e

echo "===== 开始更新代码 ====="

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置变量
read -p "应用部署路径 [默认: $PROJECT_ROOT]: " APP_PATH
APP_PATH=${APP_PATH:-$PROJECT_ROOT}

read -p "前端目录名称 [默认: front]: " FRONTEND_DIR
FRONTEND_DIR=${FRONTEND_DIR:-front}
FRONTEND_PATH="$APP_PATH/$FRONTEND_DIR"

read -p "后端目录名称 [默认: back]: " BACKEND_DIR
BACKEND_DIR=${BACKEND_DIR:-back}
BACKEND_PATH="$APP_PATH/$BACKEND_DIR"

read -p "环境配置 [默认: prod]: " ENV
ENV=${ENV:-prod}

read -p "是否拉取最新代码? (y/n) [默认: y]: " PULL_CODE
PULL_CODE=${PULL_CODE:-y}

# 拉取最新代码
if [ "$PULL_CODE" = "y" ]; then
    echo "拉取最新代码..."
    cd "$APP_PATH"

    # 检查是否是Git仓库
    if [ -d ".git" ]; then
        git pull
        echo "代码已更新"
    else
        echo "警告: 不是Git仓库，跳过代码拉取"
    fi
fi

# 更新后端
read -p "是否更新后端? (y/n) [默认: y]: " UPDATE_BACKEND
UPDATE_BACKEND=${UPDATE_BACKEND:-y}

if [ "$UPDATE_BACKEND" = "y" ]; then
    echo "更新后端..."
    cd "$BACKEND_PATH"

    # 激活虚拟环境
    if [ -d "venv" ]; then
        source venv/bin/activate
        echo "虚拟环境已激活"
    else
        echo "警告: 虚拟环境不存在，请先运行deploy_backend.sh"
        exit 1
    fi

    # 更新依赖
    echo "更新Python依赖..."
    pip install -r requirements.txt

    # 重启后端服务
    echo "重启后端服务..."
    sudo supervisorctl restart lims-backend

    echo "后端更新完成"
fi

# 更新前端
read -p "是否更新前端? (y/n) [默认: y]: " UPDATE_FRONTEND
UPDATE_FRONTEND=${UPDATE_FRONTEND:-y}

if [ "$UPDATE_FRONTEND" = "y" ]; then
    echo "更新前端..."
    cd "$FRONTEND_PATH"

    # 更新依赖
    echo "更新Node.js依赖..."
    npm install --registry=https://registry.npmmirror.com

    # 构建前端
    echo "构建前端代码..."
    if [ "$ENV" = "prod" ]; then
        npm run build:prod
    elif [ "$ENV" = "stage" ]; then
        npm run build:stage
    else
        echo "警告: 未知环境 $ENV，使用生产环境构建"
        npm run build:prod
    fi

    # 检查构建结果
    if [ ! -d "dist" ]; then
        echo "错误: 前端构建失败，dist目录不存在"
        exit 1
    fi

    # 配置前端部署目录
    read -p "前端部署目录 [默认: /var/www/html]: " DEPLOY_DIR
    DEPLOY_DIR=${DEPLOY_DIR:-/var/www/html}

    # 复制前端文件到部署目录
    echo "正在将前端文件复制到 $DEPLOY_DIR..."
    if sudo cp -r dist/* "$DEPLOY_DIR/"; then
        echo "前端文件已成功复制到 $DEPLOY_DIR"

        # 设置正确的权限
        sudo chown -R www-data:www-data "$DEPLOY_DIR"
        sudo chmod -R 755 "$DEPLOY_DIR"
        echo "已设置正确的文件权限"
    else
        echo "错误: 无法复制前端文件到 $DEPLOY_DIR，请确保有足够的权限"
        exit 1
    fi

    echo "前端更新完成"
fi

echo "===== 代码更新完成 ====="
