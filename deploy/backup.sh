#!/bin/bash
# 备份脚本
# 作用：备份数据库和上传文件

set -e

echo "===== 开始备份 ====="

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置变量
read -p "应用部署路径 [默认: $PROJECT_ROOT]: " APP_PATH
APP_PATH=${APP_PATH:-$PROJECT_ROOT}

read -p "备份目录 [默认: $APP_PATH/backups]: " BACKUP_DIR
BACKUP_DIR=${BACKUP_DIR:-$APP_PATH/backups}

read -p "数据库类型 (mysql/postgresql) [默认: mysql]: " DB_TYPE
DB_TYPE=${DB_TYPE:-mysql}

read -p "数据库名称 [默认: lims]: " DB_NAME
DB_NAME=${DB_NAME:-lims}

read -p "数据库用户名 [默认: lims-user]: " DB_USER
DB_USER=${DB_USER:-lims-user}

read -p "数据库密码: " DB_PASSWORD

read -p "上传文件目录 [默认: $APP_PATH/vf_admin/upload_path]: " UPLOAD_DIR
UPLOAD_DIR=${UPLOAD_DIR:-$APP_PATH/vf_admin/upload_path}

# 创建备份目录
mkdir -p "$BACKUP_DIR"
echo "备份目录: $BACKUP_DIR"

# 获取当前日期时间
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 备份数据库
echo "备份数据库..."
DB_BACKUP_FILE="$BACKUP_DIR/db_backup_$TIMESTAMP.sql"

if [ "$DB_TYPE" = "mysql" ]; then
    # MySQL备份
    mysqldump -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$DB_BACKUP_FILE"
elif [ "$DB_TYPE" = "postgresql" ]; then
    # PostgreSQL备份
    PGPASSWORD="$DB_PASSWORD" pg_dump -U "$DB_USER" "$DB_NAME" > "$DB_BACKUP_FILE"
else
    echo "错误: 不支持的数据库类型 $DB_TYPE"
    exit 1
fi

# 压缩数据库备份
gzip "$DB_BACKUP_FILE"
echo "数据库已备份到: ${DB_BACKUP_FILE}.gz"

# 备份上传文件
echo "备份上传文件..."
if [ -d "$UPLOAD_DIR" ]; then
    UPLOAD_BACKUP_FILE="$BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz"
    tar -czf "$UPLOAD_BACKUP_FILE" -C "$(dirname "$UPLOAD_DIR")" "$(basename "$UPLOAD_DIR")"
    echo "上传文件已备份到: $UPLOAD_BACKUP_FILE"
else
    echo "警告: 上传文件目录 $UPLOAD_DIR 不存在，跳过备份"
fi

# 清理旧备份（保留最近30天的备份）
echo "清理旧备份..."
find "$BACKUP_DIR" -name "db_backup_*.gz" -type f -mtime +30 -delete
find "$BACKUP_DIR" -name "uploads_backup_*.tar.gz" -type f -mtime +30 -delete
echo "已清理30天前的备份"

echo "===== 备份完成 ====="
