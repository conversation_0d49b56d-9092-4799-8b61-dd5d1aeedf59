#!/bin/bash
# SSL证书安装脚本
# 作用：使用Let's Encrypt安装SSL证书

set -e

echo "===== 开始安装SSL证书 ====="

# 检查是否为root用户
if [ "$(id -u)" != "0" ]; then
   echo "此脚本需要以root用户运行" 1>&2
   exit 1
fi

# 检测系统类型
if [ -f /etc/debian_version ]; then
    # Debian/Ubuntu系统
    echo "检测到Debian/Ubuntu系统"
    PKG_MANAGER="apt"
    $PKG_MANAGER update
elif [ -f /etc/redhat-release ]; then
    # CentOS/RHEL系统
    echo "检测到CentOS/RHEL系统"
    PKG_MANAGER="yum"
    $PKG_MANAGER update -y
else
    echo "不支持的系统类型"
    exit 1
fi

# 安装Certbot
echo "安装Certbot..."
if [ "$PKG_MANAGER" = "apt" ]; then
    $PKG_MANAGER install -y certbot python3-certbot-nginx
else
    $PKG_MANAGER install -y certbot python3-certbot-nginx
fi

# 获取域名
read -p "请输入您的域名: " DOMAIN

# 检查域名是否已配置
if ! grep -q "server_name $DOMAIN" /etc/nginx/sites-available/* 2>/dev/null && ! grep -q "server_name $DOMAIN" /etc/nginx/conf.d/* 2>/dev/null; then
    echo "警告: 在Nginx配置中找不到域名 $DOMAIN"
    read -p "是否继续? (y/n): " CONTINUE
    if [ "$CONTINUE" != "y" ]; then
        echo "已取消"
        exit 1
    fi
fi

# 获取邮箱
read -p "请输入您的邮箱地址 (用于Let's Encrypt通知): " EMAIL

# 安装证书
echo "正在安装SSL证书..."
certbot --nginx -d "$DOMAIN" --non-interactive --agree-tos --email "$EMAIL" --redirect

# 检查证书是否安装成功
if [ $? -eq 0 ]; then
    echo "SSL证书安装成功!"
    
    # 设置自动续期
    echo "设置证书自动续期..."
    (crontab -l 2>/dev/null; echo "0 3 * * * certbot renew --quiet") | crontab -
    echo "已添加自动续期任务到crontab"
    
    # 获取脚本所在目录的绝对路径
    SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
    
    # 更新Nginx SSL配置
    echo "更新Nginx SSL配置..."
    NGINX_SSL_CONF="$SCRIPT_DIR/nginx_ssl.conf"
    
    # 替换配置文件中的域名
    sed -i "s/your-domain.com/$DOMAIN/g" "$NGINX_SSL_CONF"
    
    echo "请根据需要修改 $NGINX_SSL_CONF 文件，然后将其应用到您的Nginx配置中"
else
    echo "SSL证书安装失败，请检查错误信息"
fi

echo "===== SSL证书安装完成 ====="
