services:
  # MySQL 数据库
  mysql:
    image: docker.xuanyuan.me/mysql:8.0
    container_name: lims-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: lims
      MYSQL_USER: lims-user
      MYSQL_PASSWORD: lims-Root1
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ../back/sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - lims-dev-network

  # Redis 缓存
  redis:
    image: docker.xuanyuan.me/redis:7-alpine
    container_name: lims-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - lims-dev-network

  # 后端服务 (开发模式，挂载源码)
  backend:
    build:
      context: ../back
      dockerfile: ../docker/backend/Dockerfile.dev
    container_name: lims-backend-dev
    restart: unless-stopped
    environment:
      - APP_ENV=dev
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=lims-user
      - DB_PASSWORD=lims-Root1
      - DB_DATABASE=lims
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "9099:9099"
    volumes:
      - ../back:/app
      - /app/venv
      - /app/__pycache__
    depends_on:
      - mysql
      - redis
    networks:
      - lims-dev-network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  lims-dev-network:
    driver: bridge
