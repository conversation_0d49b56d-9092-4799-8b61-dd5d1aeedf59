services:
  # MySQL 数据库
  mysql:
    image: docker.xuanyuan.me/mysql:8.0
    container_name: lims-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: lims
      MYSQL_USER: lims-user
      MYSQL_PASSWORD: lims-Root1
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ../back/sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - lims-network

  # Redis 缓存
  redis:
    image: docker.xuanyuan.me/redis:7-alpine
    container_name: lims-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lims-network

  # 后端服务
  backend:
    build:
      context: ../back
      dockerfile: ../docker/backend/Dockerfile
    container_name: lims-backend
    restart: unless-stopped
    environment:
      - APP_ENV=prod
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=lims-user
      - DB_PASSWORD=lims-Root1
      - DB_DATABASE=lims
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "9099:9099"
    volumes:
      - ../back/vf_admin/upload_path:/app/vf_admin/upload_path
      - ../back/logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - lims-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9099/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ../front
      dockerfile: ../docker/frontend/Dockerfile
    container_name: lims-frontend
    restart: unless-stopped
    ports:
      - "4000:80"
    depends_on:
      - backend
    networks:
      - lims-network

  # Nginx 反向代理
  nginx:
    image: docker.xuanyuan.me/nginx:alpine
    container_name: lims-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ../back/vf_admin/upload_path:/var/www/upload:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - lims-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  lims-network:
    driver: bridge
