#!/bin/bash

# Docker 镜像源切换脚本
# 用于在不同的镜像仓库之间切换

echo "===== Docker 镜像源切换工具 ====="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 定义镜像源
declare -A REGISTRIES=(
    ["xuanyuan"]="docker.xuanyuan.me"
    ["aliyun"]="registry.cn-hangzhou.aliyuncs.com/library"
    ["tencent"]="ccr.ccs.tencentyun.com/library"
    ["dockerhub"]=""
)

# 显示可用的镜像源
show_registries() {
    echo "可用的镜像源："
    echo "1. xuanyuan   - Xuanyuan 镜像源 (默认)"
    echo "2. aliyun     - 阿里云镜像源"
    echo "3. tencent    - 腾讯云镜像源"
    echo "4. dockerhub  - Docker Hub 官方源"
}

# 替换文件中的镜像源
replace_registry() {
    local file=$1
    local old_registry=$2
    local new_registry=$3
    
    if [ ! -f "$file" ]; then
        log_warn "文件不存在: $file"
        return 1
    fi
    
    log_info "更新文件: $file"
    
    # 备份原文件
    cp "$file" "$file.bak"
    
    if [ -z "$new_registry" ]; then
        # Docker Hub 官方源，移除镜像源前缀
        if [ -n "$old_registry" ]; then
            sed -i "s|$old_registry/||g" "$file"
        fi
    else
        # 其他镜像源
        if [ -z "$old_registry" ]; then
            # 从 Docker Hub 切换到其他源
            sed -i "s|FROM \([^/]*\):|FROM $new_registry/\1:|g" "$file"
            sed -i "s|image: \([^/]*\):|image: $new_registry/\1:|g" "$file"
        else
            # 在镜像源之间切换
            sed -i "s|$old_registry|$new_registry|g" "$file"
        fi
    fi
}

# 获取当前使用的镜像源
get_current_registry() {
    if grep -q "docker.xuanyuan.me" docker-compose.yml 2>/dev/null; then
        echo "xuanyuan"
    elif grep -q "registry.cn-hangzhou.aliyuncs.com" docker-compose.yml 2>/dev/null; then
        echo "aliyun"
    elif grep -q "ccr.ccs.tencentyun.com" docker-compose.yml 2>/dev/null; then
        echo "tencent"
    else
        echo "dockerhub"
    fi
}

# 切换镜像源
switch_registry() {
    local target_registry=$1
    
    if [ -z "$target_registry" ] || [ -z "${REGISTRIES[$target_registry]}" ]; then
        log_error "无效的镜像源: $target_registry"
        show_registries
        return 1
    fi
    
    local current_registry=$(get_current_registry)
    local current_url="${REGISTRIES[$current_registry]}"
    local target_url="${REGISTRIES[$target_registry]}"
    
    if [ "$current_registry" = "$target_registry" ]; then
        log_info "当前已经使用 $target_registry 镜像源"
        return 0
    fi
    
    log_step "从 $current_registry 切换到 $target_registry"
    
    # 需要更新的文件列表
    local files=(
        "docker-compose.yml"
        "docker-compose.dev.yml"
        "backend/Dockerfile"
        "backend/Dockerfile.dev"
        "frontend/Dockerfile"
    )
    
    # 更新所有文件
    for file in "${files[@]}"; do
        replace_registry "$file" "$current_url" "$target_url"
    done
    
    log_info "镜像源切换完成: $current_registry -> $target_registry"
    log_warn "建议重新构建镜像: docker compose build --no-cache"
}

# 测试镜像源连通性
test_registry() {
    local registry_name=$1
    local registry_url="${REGISTRIES[$registry_name]}"
    
    log_step "测试 $registry_name 镜像源连通性..."
    
    if [ "$registry_name" = "dockerhub" ]; then
        if curl -s --connect-timeout 10 https://registry-1.docker.io/v2/ > /dev/null; then
            log_info "$registry_name 连接正常"
            return 0
        else
            log_error "$registry_name 连接失败"
            return 1
        fi
    else
        if curl -s --connect-timeout 10 "https://$registry_url/v2/" > /dev/null; then
            log_info "$registry_name 连接正常"
            return 0
        else
            log_error "$registry_name 连接失败"
            return 1
        fi
    fi
}

# 自动选择最佳镜像源
auto_select() {
    log_step "自动检测最佳镜像源..."
    
    local best_registry=""
    local registries_to_test=("xuanyuan" "aliyun" "tencent" "dockerhub")
    
    for registry in "${registries_to_test[@]}"; do
        if test_registry "$registry"; then
            best_registry=$registry
            break
        fi
    done
    
    if [ -n "$best_registry" ]; then
        log_info "推荐使用: $best_registry"
        read -p "是否切换到 $best_registry? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            switch_registry "$best_registry"
        fi
    else
        log_error "所有镜像源都无法连接"
        return 1
    fi
}

# 恢复备份
restore_backup() {
    log_step "恢复备份文件..."
    
    local files=(
        "docker-compose.yml"
        "docker-compose.dev.yml"
        "backend/Dockerfile"
        "backend/Dockerfile.dev"
        "frontend/Dockerfile"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file.bak" ]; then
            mv "$file.bak" "$file"
            log_info "已恢复: $file"
        fi
    done
}

# 主函数
main() {
    local current_registry=$(get_current_registry)
    local current_url="${REGISTRIES[$current_registry]}"
    
    echo "当前镜像源: $current_registry"
    if [ -n "$current_url" ]; then
        echo "当前地址: $current_url"
    else
        echo "当前地址: Docker Hub 官方源"
    fi
    echo ""
    
    case "$1" in
        "list")
            show_registries
            ;;
        "test")
            if [ -n "$2" ]; then
                test_registry "$2"
            else
                log_info "测试所有镜像源..."
                for registry in "${!REGISTRIES[@]}"; do
                    test_registry "$registry"
                done
            fi
            ;;
        "switch")
            if [ -n "$2" ]; then
                switch_registry "$2"
            else
                log_error "请指定要切换的镜像源"
                show_registries
            fi
            ;;
        "auto")
            auto_select
            ;;
        "restore")
            restore_backup
            ;;
        *)
            echo "用法: $0 {list|test|switch|auto|restore} [registry_name]"
            echo ""
            echo "命令说明:"
            echo "  list              - 显示可用的镜像源"
            echo "  test [registry]   - 测试镜像源连通性"
            echo "  switch <registry> - 切换到指定镜像源"
            echo "  auto              - 自动选择最佳镜像源"
            echo "  restore           - 恢复备份文件"
            echo ""
            show_registries
            ;;
    esac
}

# 执行主函数
main "$@"
