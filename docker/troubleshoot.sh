#!/bin/bash

# Docker 部署故障排除脚本

echo "===== LIMS Docker 部署故障排除 ====="

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查 Docker 环境
check_docker_env() {
    log_step "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        echo "请安装 Docker: https://docs.docker.com/get-docker/"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        echo "请启动 Docker 服务: sudo systemctl start docker"
        return 1
    fi
    
    log_info "Docker 版本: $(docker --version)"
    
    # 检查 Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose V1: $(docker-compose --version)"
    elif docker compose version &> /dev/null; then
        log_info "Docker Compose V2: $(docker compose version)"
    else
        log_error "Docker Compose 未安装"
        return 1
    fi
    
    return 0
}

# 检查网络连接
check_network() {
    log_step "检查网络连接..."
    
    # 测试基本网络连接
    if ! ping -c 1 8.8.8.8 &> /dev/null; then
        log_error "网络连接异常"
        return 1
    fi
    
    # 测试 Docker Hub
    if curl -s --connect-timeout 5 https://registry-1.docker.io/v2/ > /dev/null; then
        log_info "Docker Hub 连接正常"
    else
        log_warn "Docker Hub 连接异常，建议使用国内镜像源"
    fi
    
    # 测试 Xuanyuan 镜像仓库
    if curl -s --connect-timeout 5 https://docker.xuanyuan.me/v2/ > /dev/null; then
        log_info "Xuanyuan 镜像仓库连接正常"
    else
        log_warn "Xuanyuan 镜像仓库连接异常"
    fi
    
    return 0
}

# 检查端口占用
check_ports() {
    log_step "检查端口占用..."
    
    local ports=(80 443 3306 6379 9099 4000)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep ":$port " > /dev/null || \
           ss -tuln 2>/dev/null | grep ":$port " > /dev/null; then
            occupied_ports+=($port)
            log_warn "端口 $port 已被占用"
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo "被占用的端口: ${occupied_ports[*]}"
        echo "请停止占用这些端口的服务，或修改 docker-compose.yml 中的端口配置"
        return 1
    else
        log_info "所有必需端口都可用"
        return 0
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_step "检查磁盘空间..."
    
    local available=$(df . | awk 'NR==2 {print $4}')
    local available_gb=$((available / 1024 / 1024))
    
    if [ $available_gb -lt 5 ]; then
        log_error "磁盘空间不足，可用空间: ${available_gb}GB，建议至少 5GB"
        return 1
    else
        log_info "磁盘空间充足，可用空间: ${available_gb}GB"
        return 0
    fi
}

# 检查内存
check_memory() {
    log_step "检查内存..."
    
    local total_mem=$(free -m | awk 'NR==2{print $2}')
    local available_mem=$(free -m | awk 'NR==2{print $7}')
    
    if [ $available_mem -lt 2048 ]; then
        log_warn "可用内存较少: ${available_mem}MB，建议至少 2GB"
    else
        log_info "内存充足，可用内存: ${available_mem}MB"
    fi
    
    return 0
}

# 检查文件权限
check_permissions() {
    log_step "检查文件权限..."
    
    if [ ! -r "docker-compose.yml" ]; then
        log_error "无法读取 docker-compose.yml"
        return 1
    fi
    
    if [ ! -x "deploy.sh" ]; then
        log_warn "deploy.sh 没有执行权限，正在修复..."
        chmod +x deploy.sh
    fi
    
    # 检查必要目录
    local dirs=("../back/logs" "../back/vf_admin/upload_path" "mysql/conf.d" "nginx/conf.d")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_warn "目录不存在: $dir，正在创建..."
            mkdir -p "$dir"
        fi
    done
    
    log_info "文件权限检查完成"
    return 0
}

# 清理 Docker 资源
cleanup_docker() {
    log_step "清理 Docker 资源..."
    
    echo "这将清理未使用的 Docker 镜像、容器和网络"
    read -p "是否继续? (y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        docker system prune -f
        log_info "Docker 资源清理完成"
    else
        log_info "跳过 Docker 资源清理"
    fi
}

# 显示系统信息
show_system_info() {
    log_step "系统信息..."
    
    echo "操作系统: $(uname -a)"
    echo "CPU 核心数: $(nproc)"
    echo "总内存: $(free -h | awk 'NR==2{print $2}')"
    echo "磁盘使用情况:"
    df -h . | head -2
    echo ""
}

# 主函数
main() {
    show_system_info
    
    local checks=(
        "check_docker_env"
        "check_network" 
        "check_ports"
        "check_disk_space"
        "check_memory"
        "check_permissions"
    )
    
    local failed_checks=()
    
    for check in "${checks[@]}"; do
        if ! $check; then
            failed_checks+=($check)
        fi
        echo ""
    done
    
    if [ ${#failed_checks[@]} -eq 0 ]; then
        log_info "所有检查都通过，环境准备就绪！"
        echo ""
        echo "现在可以运行部署脚本:"
        echo "  ./deploy.sh"
    else
        log_error "发现 ${#failed_checks[@]} 个问题，请先解决这些问题："
        for check in "${failed_checks[@]}"; do
            echo "  - $check"
        done
        echo ""
        echo "如需清理 Docker 资源，可以运行:"
        echo "  $0 --cleanup"
    fi
}

# 处理命令行参数
if [[ "$1" == "--cleanup" ]]; then
    cleanup_docker
else
    main "$@"
fi
