#!/bin/bash

# LIMS 项目 Docker 部署脚本
# 作用：一键部署整个 LIMS 项目

set -e

echo "===== LIMS 项目 Docker 部署脚本 ====="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_docker() {
    log_info "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose 版本
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
        log_info "使用 Docker Compose V1"
    elif docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
        log_info "使用 Docker Compose V2"
    else
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "Docker 环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    mkdir -p mysql/conf.d
    mkdir -p nginx/conf.d
    mkdir -p ssl
    mkdir -p ../back/logs
    mkdir -p ../back/vf_admin/upload_path
    mkdir -p ../back/vf_admin/download_path
    mkdir -p ../back/vf_admin/gen_path

    log_info "目录创建完成"
}

# 构建和启动服务
deploy_services() {
    log_info "开始构建和启动服务..."

    # 停止现有服务
    log_info "停止现有服务..."
    $COMPOSE_CMD down || true

    # 构建镜像
    log_info "构建 Docker 镜像..."
    $COMPOSE_CMD build --no-cache

    # 启动服务
    log_info "启动服务..."
    $COMPOSE_CMD up -d

    log_info "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待 MySQL
    log_info "等待 MySQL 服务就绪..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if $COMPOSE_CMD exec -T mysql mysqladmin ping -h localhost --silent 2>/dev/null; then
            log_info "MySQL 服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "MySQL 服务启动超时"
        exit 1
    fi
    
    # 等待后端服务
    log_info "等待后端服务就绪..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:9099/health 2>/dev/null; then
            log_info "后端服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_warn "后端服务可能未完全就绪，请检查日志"
    fi
    
    log_info "服务就绪检查完成"
}

# 显示部署信息
show_deployment_info() {
    log_info "===== 部署完成 ====="
    echo ""
    echo "访问地址："
    echo "  前端地址: http://localhost"
    echo "  后端API: http://localhost/dev-api"
    echo "  API文档: http://localhost/dev-api/docs"
    echo ""
    echo "默认账号："
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo ""
    echo "服务状态："
    $COMPOSE_CMD ps
    echo ""
    echo "常用命令："
    echo "  查看日志: $COMPOSE_CMD logs -f [service_name]"
    echo "  重启服务: $COMPOSE_CMD restart [service_name]"
    echo "  停止服务: $COMPOSE_CMD down"
    echo "  更新服务: $COMPOSE_CMD up -d --build"
}

# 主函数
main() {
    check_docker
    create_directories
    deploy_services
    wait_for_services
    show_deployment_info
}

# 执行主函数
main "$@"
